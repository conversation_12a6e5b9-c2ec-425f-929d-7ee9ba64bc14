<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Rating - Positions</h2>
                    <p class="text-muted mb-0">Select a position to view applications</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/position-groups/' . $exercise['id']) ?>" class="text-decoration-none">
                                <?= esc($exercise['exercise_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page"><?= esc($positionGroup['group_name']) ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Position Group Info Card -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Position Group Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2"><strong>Group Name:</strong> <?= esc($positionGroup['group_name']) ?></p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2"><strong>Exercise:</strong> <?= esc($exercise['exercise_name']) ?></p>
                </div>
            </div>
            <?php if (!empty($positionGroup['description'])): ?>
                <div class="mt-3">
                    <p class="mb-0"><strong>Description:</strong> <?= esc($positionGroup['description']) ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Positions List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Positions</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Position</th>
                            <th>Reference</th>
                            <th>Classification</th>
                            <th>Location</th>
                            <th>Annual Salary</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($positions)): ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">No positions found with applications that have completed profiling</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td class="px-4 fw-medium"><?= esc($position['designation']) ?></td>
                                    <td><?= esc($position['position_reference']) ?></td>
                                    <td><?= esc($position['classification']) ?></td>
                                    <td><?= esc($position['location']) ?></td>
                                    <td><?= esc($position['annual_salary']) ?></td>
                                    <td>
                                        <a href="<?= base_url('rating/applications/' . $position['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-arrow-right me-1"></i> View Applications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Positions page loaded');
});
</script>
<?= $this->endSection() ?> 