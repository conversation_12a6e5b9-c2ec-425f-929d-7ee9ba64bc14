<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Dakoii Admin" content="This is Dakoii Admin Interface" />
    <link rel="shortcut icon" href="<?= base_url() ?>/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Font Awesome - Local Installation -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css">

    <!-- Google Fonts - Roboto -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS for dark theme -->
    <style>
        :root {
            /* Primary and accent colors with better contrast */
            --primary-color: #5e97f6;      /* Brightened blue */
            --primary-hover: #7ba9f7;      /* Lighter blue for hover states */
            --secondary-color: #4bd0a0;    /* Brightened green */
            --secondary-hover: #67e5b5;    /* Lighter green for hover */
            --accent-color: #ffcc80;       /* Soft orange accent */

            /* Background colors - shaded dark theme */
            --dark-bg: #1e2235;           /* Main background */
            --darker-bg: #171a29;         /* Darker areas like navbar */
            --light-bg: #2a2f45;          /* Card backgrounds */
            --lighter-bg: #353b50;        /* Highlighted areas */

            /* Text colors with proper contrast */
            --light-text: #ffffff;         /* Primary text - pure white for maximum contrast */
            --secondary-text: #e0e0e0;     /* Secondary text - slightly dimmed */
            --muted-text: #b4bac6;        /* Muted text for less emphasis */
            --link-text: #82b1ff;         /* Links - bright blue */

            /* Component colors */
            --card-bg: #2a2f45;           /* Card backgrounds */
            --border-color: #3c4358;      /* Borders */
            --input-bg: rgba(255, 255, 255, 0.08); /* Slightly lighter input fields */
            --input-border: #4c5366;      /* More visible input borders */
            --hover-bg: rgba(94, 151, 246, 0.15); /* Hover background */

            /* Alert and status colors */
            --success-color: #4cd964;     /* Brighter success green */
            --warning-color: #ffcc00;     /* More visible warning yellow */
            --danger-color: #ff3b30;      /* Brighter danger red */
            --info-color: #5ac8fa;        /* Brighter info blue */
        }

        body {
            background-color: var(--dark-bg);
            color: var(--light-text);
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--darker-bg) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .navbar-brand {
            color: var(--light-text) !important;
            font-weight: 700;
        }

        .nav-link {
            color: var(--secondary-text) !important;
            font-weight: 500;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--light-text) !important;
            background-color: rgba(94, 151, 246, 0.15);
        }

        .footer {
            background-color: var(--darker-bg) !important;
            border-top: 1px solid var(--border-color);
            padding: 1rem 0;
            position: relative;
            z-index: 1;
        }

        /* Form controls with better contrast */
        .form-control, .input-group-text {
            background-color: var(--input-bg);
            color: var(--light-text);
            border-color: var(--input-border);
        }

        .form-control::placeholder {
            color: var(--muted-text);
            opacity: 0.7;
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.12);
            color: var(--light-text);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(94, 151, 246, 0.25);
        }

        .form-label {
            color: var(--secondary-text);
            font-weight: 500;
        }

        /* Buttons with enhanced contrast */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--light-text);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        /* Enhanced alerts */
        .alert-danger {
            background-color: rgba(255, 59, 48, 0.15);
            color: #ff6b62;
            border-color: rgba(255, 59, 48, 0.3);
            font-weight: 500;
        }

        /* Links with improved visibility */
        a {
            color: var(--link-text);
            text-decoration: none;
            transition: color 0.2s;
        }

        a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        /* Card styling with improved contrast */
        .card {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        .card-header, .card-footer {
            background-color: rgba(0, 0, 0, 0.15);
            border-color: var(--border-color);
        }

        /* Text utilities for dark theme */
        .text-muted {
            color: var(--muted-text) !important;
        }

        .text-white {
            color: var(--light-text) !important;
        }

        /* Improve badge contrast */
        .badge {
            font-weight: 600;
        }

        .badge-light, .bg-light {
            background-color: rgba(255, 255, 255, 0.15) !important;
            color: var(--light-text) !important;
        }

        .login-container {
            max-width: 450px;
            margin: 100px auto;
            padding: 30px;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--border-color);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: var(--light-text);
            font-weight: 600;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .login-header p {
            color: var(--secondary-text);
            font-size: 1rem;
        }

        .form-control {
            background-color: var(--input-bg);
            border: 1px solid var(--input-border);
            color: var(--light-text);
            padding: 12px 15px;
            height: auto;
            font-size: 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.12);
            border-color: var(--primary-color);
            color: var(--light-text);
            box-shadow: 0 0 0 0.25rem rgba(94, 151, 246, 0.25);
        }

        .form-control::placeholder {
            color: var(--muted-text);
            opacity: 0.7;
        }

        .form-label {
            color: var(--secondary-text);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--light-text);
            padding: 10px 20px;
            font-weight: 500;
            border-radius: 6px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .alert {
            border-radius: 8px;
            border: none;
            font-weight: 500;
            padding: 15px;
        }

        .alert-success {
            background-color: rgba(76, 217, 100, 0.15) !important;
            color: #65e87b !important;
            border-left: 4px solid var(--success-color) !important;
        }

        .alert-danger {
            background-color: rgba(255, 59, 48, 0.15) !important;
            color: #ff6b62 !important;
            border-left: 4px solid var(--danger-color) !important;
        }

        .alert-warning {
            background-color: rgba(255, 204, 0, 0.15) !important;
            color: #ffdb4d !important;
            border-left: 4px solid var(--warning-color) !important;
        }

        .alert-info {
            background-color: rgba(90, 200, 250, 0.15) !important;
            color: #7ad1fb !important;
            border-left: 4px solid var(--info-color) !important;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: var(--secondary-text);
        }

        .input-group-text {
            background-color: var(--input-bg);
            border-color: var(--input-border);
            color: var(--secondary-text);
        }

        /* Custom checkbox design */
        .form-check-input {
            background-color: var(--input-bg);
            border-color: var(--input-border);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            color: var(--secondary-text);
        }
    </style>

    <title><?= $title ?? 'Dakoii Admin' ?></title>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Brand Logo" width="30" height="30" class="d-inline-block align-text-top me-2">
                <?= SYSTEM_NAME ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                        <?php $active = isset($menu) && $menu == "dlogin" ? "active" : ""; ?>
                        <a class="nav-link <?= $active ?>" href="<?= base_url() ?>">
                            <i class="fas fa-arrow-left me-1"></i> Back to Home
                        </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

    <?= $this->renderSection('content') ?>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>