<?php

namespace App\Controllers;

class HomeAuthController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    // Admin Login Methods
    public function loginForm()
    {
        return view('home/home_login', [
            'title' => 'Admin Login',
            'menu' => 'login'
        ]);
    }

    public function processLogin()
    {
        if (!$this->request->getPost('username') || !$this->request->getPost('password')) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        // Mock login for UI development - accept any credentials
        if ($username && $password) {
            $this->session->set([
                'logged_in' => true,
                'user_id' => 1,
                'name' => 'Demo Admin',
                'role' => 'admin',
                'org_id' => 1
            ]);

            return redirect()->to('dashboard')->with('success', "Welcome back, Demo Admin! You've successfully logged in.");
        }

        return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
    }

    // Applicant Registration Methods
    public function registerForm()
    {
        return view('home/home_register', [
            'title' => 'Create Account',
            'menu' => 'register'
        ]);
    }

    public function processRegister()
    {
        $rules = [
            'firstname' => 'required|min_length[2]|max_length[50]',
            'lastname' => 'required|min_length[2]|max_length[50]',
            'email' => 'required|valid_email',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Mock registration for UI development - just show success
        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Account Created');
        session()->setFlashdata('swal_text', 'Your account has been created successfully. Please check your email for activation instructions.');

        return redirect()->to('/');
    }

    // Applicant Login Methods
    public function applicantLoginForm()
    {
        return view('home/home_applicant_login', [
            'title' => 'Applicant Login',
            'menu' => 'applicant_login'
        ]);
    }

    public function processApplicantLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        if (!$email || !$password) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Missing Information');
            session()->setFlashdata('swal_text', 'Please enter both email and password.');
            return redirect()->back();
        }

        // Mock applicant login for UI development - accept any credentials
        if ($email && $password) {
            $this->session->set([
                'logged_in' => true,
                'applicant_id' => 1,
                'applicant_name' => 'Demo Applicant',
                'applicant_email' => $email
            ]);

            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Login Successful');
            session()->setFlashdata('swal_text', 'Welcome back, Demo Applicant!');
            return redirect()->to('applicant/dashboard');
        }

        session()->setFlashdata('swal_icon', 'error');
        session()->setFlashdata('swal_title', 'Login Failed');
        session()->setFlashdata('swal_text', 'Invalid email or password. Please try again.');
        return redirect()->back();
    }

    // Account Activation
    public function activate($token)
    {
        // Mock activation for UI development - always show success
        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Account Activated');
        session()->setFlashdata('swal_text', 'Your account has been activated successfully. You can now login.');
        return redirect()->to('/');
    }
}
