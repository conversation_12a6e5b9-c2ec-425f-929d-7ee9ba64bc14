<?php
/**
 * View file for listing unacknowledged applications
 *
 * @var array $applications List of applications pending acknowledgment
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />

    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-inbox me-2"></i>Incoming Applications</h2>
            <p class="text-muted">Applications waiting for acknowledgment</p>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                <i class="fas fa-arrow-left me-2"></i>Back to Previous Page
            </button>
            <button id="batchAcknowledgeBtn" class="btn btn-primary" disabled>
                <i class="fas fa-check-circle me-1"></i> Acknowledge Selected
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <?php if (empty($applications)): ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Incoming Applications</h4>
                        <p class="text-muted">All applications have been acknowledged.</p>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Applications Pending Acknowledgment (<?= count($applications) ?>)</h5>
                    </div>
                    <div class="table-responsive">
                        <table id="applicationsTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Application #</th>
                                    <th>Applicant Name</th>
                                    <th>Position Applied</th>
                                    <th>Date Applied</th>
                                    <th>Status</th>
                                    <th width="200">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($applications as $app): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="app-checkbox form-check-input"
                                                   value="<?= $app['id'] ?>">
                                        </td>
                                        <td><?= $app['application_number'] ?></td>
                                        <td><?= $app['fname'] . ' ' . $app['lname'] ?></td>
                                        <td>
                                            <div>
                                                <strong><?= esc($app['position_title']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= esc($app['position_reference']) ?></small>
                                                <br>
                                                <small class="text-info"><?= esc($app['org_name']) ?></small>
                                            </div>
                                        </td>
                                        <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-warning">Pending</span>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('incoming_applicants/view/' . $app['id']) ?>"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <button type="button" class="btn btn-sm btn-success acknowledge-btn"
                                                    data-id="<?= $app['id'] ?>">
                                                <i class="fas fa-check-circle"></i> Acknowledge
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#applicationsTable').DataTable({
        responsive: true,
        order: [[4, 'desc']], // Sort by date applied desc
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications available",
        }
    });

    // Handle select all checkbox
    $('#selectAll').change(function() {
        const isChecked = $(this).prop('checked');
        $('.app-checkbox').prop('checked', isChecked);
        updateBatchButton();
    });

    // Handle individual checkboxes
    $(document).on('change', '.app-checkbox', function() {
        updateBatchButton();

        // If any checkbox is unchecked, uncheck the "select all" checkbox
        if (!$(this).prop('checked')) {
            $('#selectAll').prop('checked', false);
        }

        // If all checkboxes are checked, check the "select all" checkbox
        const totalCheckboxes = $('.app-checkbox').length;
        const checkedCheckboxes = $('.app-checkbox:checked').length;
        if (totalCheckboxes === checkedCheckboxes) {
            $('#selectAll').prop('checked', true);
        }
    });

    // Update batch acknowledge button state
    function updateBatchButton() {
        const selectedCount = $('.app-checkbox:checked').length;
        $('#batchAcknowledgeBtn').prop('disabled', selectedCount === 0)
            .text(selectedCount > 0 ? `Acknowledge Selected (${selectedCount})` : 'Acknowledge Selected');
    }

    // Handle acknowledge button click (individual)
    $(document).on('click', '.acknowledge-btn', function() {
        const id = $(this).data('id');
        acknowledgeApplication(id);
    });

    // Handle batch acknowledge button click
    $('#batchAcknowledgeBtn').click(function() {
        const selectedIds = [];
        $('.app-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });

        // Confirm batch acknowledge
        if (confirm(`Are you sure you want to acknowledge ${selectedIds.length} application(s)?`)) {
            batchAcknowledgeApplications(selectedIds);
        }
    });

    // Function to acknowledge a single application
    function acknowledgeApplication(id) {
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';

        // Create data object
        const data = {};
        data[csrfName] = csrfToken;

        $.ajax({
            url: `<?= base_url('incoming_applicants/acknowledge') ?>/${id}`,
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function() {
                // Disable buttons to prevent double submission
                $(`.acknowledge-btn[data-id="${id}"]`).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message);

                    // Update CSRF hash with the new one from the response
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }

                    // Remove the row from the table
                    table.row($(`.app-checkbox[value="${id}"]`).closest('tr')).remove().draw();

                    // Update UI as needed
                    updateBatchButton();
                } else {
                    // Show error message
                    toastr.error(response.message);

                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }

                    // Re-enable the button
                    $(`.acknowledge-btn[data-id="${id}"]`).prop('disabled', false).html('<i class="fas fa-check-circle"></i> Acknowledge');
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');

                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }

                // Re-enable the button
                $(`.acknowledge-btn[data-id="${id}"]`).prop('disabled', false).html('<i class="fas fa-check-circle"></i> Acknowledge');
            }
        });
    }

    // Function to batch acknowledge applications
    function batchAcknowledgeApplications(ids) {
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';

        // Create data object
        const data = {
            ids: ids
        };
        data[csrfName] = csrfToken;

        $.ajax({
            url: '<?= base_url('incoming_applicants/batch_acknowledge') ?>',
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function() {
                // Disable button to prevent double submission
                $('#batchAcknowledgeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message);

                    // Update CSRF hash with the new one from the response
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }

                    // Remove the acknowledged applications from the table
                    ids.forEach(function(id) {
                        table.row($(`.app-checkbox[value="${id}"]`).closest('tr')).remove().draw();
                    });

                    // Uncheck select all checkbox
                    $('#selectAll').prop('checked', false);

                    // Update UI as needed
                    updateBatchButton();
                } else {
                    // Show error message
                    toastr.error(response.message);

                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }

                    // Re-enable the button
                    $('#batchAcknowledgeBtn').prop('disabled', false).text(`Acknowledge Selected (${ids.length})`);
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');

                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }

                // Re-enable the button
                $('#batchAcknowledgeBtn').prop('disabled', false).text(`Acknowledge Selected (${ids.length})`);
            }
        });
    }
});
</script>
<?= $this->endSection() ?>
