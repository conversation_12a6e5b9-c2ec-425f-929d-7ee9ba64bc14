<?php
/**
 * View file for listing applications for a specific position that need pre-screening
 * 
 * @var array $applications List of applications pending pre-screening for this position
 * @var array $position Position details
 * @var array $positionGroup Position group details 
 * @var array $exercise Exercise details
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
    
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-filter me-2"></i>Position Applications: <?= esc($position['designation']) ?></h2>
            <p class="text-muted">All acknowledged applications for this position</p>
            <?php if ($exercise && $positionGroup): ?>
            <p><strong>Exercise:</strong> <?= esc($exercise['exercise_name'] ?? 'None') ?> | <strong>Position Group:</strong> <?= esc($positionGroup['group_name']) ?></p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if ($exercise && $positionGroup): ?>
            <a href="<?= base_url('applications_pre_screening_exercise/group/' . $positionGroup['id']) ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
            <?php elseif ($exercise): ?>
            <a href="<?= base_url('applications_pre_screening_exercise/view/' . $exercise['id']) ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercise
            </a>
            <?php else: ?>
            <a href="<?= base_url('applications_pre_screening_exercise') ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
            <?php endif; ?>
            <button id="batchPreScreenBtn" class="btn btn-primary" disabled>
                <i class="fas fa-check me-1"></i> Pre-Screen Selected
            </button>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Position Details</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Position Name:</strong> <?= esc($position['designation']) ?></p>
                    <p><strong>Grade:</strong> <?= esc($position['classification'] ?? 'N/A') ?></p>
                </div>
                <div class="col-md-4">
                    <p><strong>Position Group:</strong> <?= $positionGroup ? esc($positionGroup['group_name']) : 'None' ?></p>
                </div>
                <div class="col-md-4">
                    <p><strong>Exercise:</strong> <?= $exercise ? esc($exercise['exercise_name'] ?? 'None') : 'None' ?></p>
                    <p><strong>Advertisement No:</strong> <?= $exercise ? esc($exercise['advertisement_no'] ?? 'N/A') : 'N/A' ?></p>
                </div>
            </div>
            <?php if (!empty($position['description'])): ?>
                <hr>
                <h6 class="fw-bold">Description:</h6>
                <p><?= nl2br(esc($position['description'] ?? '')) ?></p>
            <?php endif; ?>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No acknowledged applications found for this position.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="applicationsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th width="10%">App. Number</th>
                                <th width="20%">Applicant Name</th>
                                <th width="15%">Date Applied</th>
                                <th width="15%">Date Acknowledged</th>
                                <th width="15%">Pre-Screen Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $app): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="app-checkbox form-check-input" 
                                               value="<?= $app['id'] ?>">
                                    </td>
                                    <td><?= $app['application_number'] ?></td>
                                    <td><?= $app['fname'] . ' ' . $app['lname'] ?></td>
                                    <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                    <td><?= date('d M Y', strtotime($app['recieved_acknowledged'])) ?></td>
                                    <td>
                                        <?php if ($app['pre_screened_status']): ?>
                                            <span class="badge <?= $app['pre_screened_status'] == 'passed' ? 'bg-success' : ($app['pre_screened_status'] == 'failed' ? 'bg-danger' : 'bg-warning') ?>">
                                                <?= ucfirst($app['pre_screened_status']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('application_pre_screening/viewApplication') ?>/<?= $app['id'] ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-clipboard-check"></i> Detailed View
                                        </a>
                                        <?php if (!$app['pre_screened_status']): ?>
                                        <button type="button" class="btn btn-sm btn-success pre-screen-btn"
                                                data-id="<?= $app['id'] ?>">
                                            <i class="fas fa-check-circle"></i> Pre-Screen
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Pre-Screen Application Modal -->
<div class="modal fade" id="preScreenModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">Pre-Screen Application</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="preScreenForm">
                    <input type="hidden" id="applicationId" name="application_id">
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Pre-Screening Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="passed">Passed</option>
                            <option value="failed">Failed</option>
                            <option value="review_needed">Needs Further Review</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Enter any notes or observations from pre-screening"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="savePreScreenBtn">
                    <i class="fas fa-save me-1"></i> Save Pre-Screening Result
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Pre-Screen Modal -->
<div class="modal fade" id="batchPreScreenModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">Batch Pre-Screen Applications</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchPreScreenForm">
                    <div class="mb-3">
                        <label for="batchStatus" class="form-label">Pre-Screening Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="batchStatus" name="status" required>
                            <option value="">Select Status</option>
                            <option value="passed">Passed</option>
                            <option value="failed">Failed</option>
                            <option value="review_needed">Needs Further Review</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="batchRemarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="batchRemarks" name="remarks" rows="3" placeholder="Enter comments that apply to all selected applications"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="saveBatchPreScreenBtn">
                    <i class="fas fa-save me-1"></i> Save Pre-Screening Results
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add global AJAX error handler for 401 unauthorized responses (session timeout)
    $(document).ajaxError(function(event, jqXHR, settings, thrownError) {
        if (jqXHR.status === 401) {
            try {
                const response = JSON.parse(jqXHR.responseText);
                if (response.redirect) {
                    toastr.error(response.message || 'Your session has expired. Please login again.');
                    setTimeout(function() {
                        window.location.href = response.redirect;
                    }, 2000); // Redirect after 2 seconds
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                toastr.error('Your session has expired. Please login again.');
                setTimeout(function() {
                    window.location.href = '<?= base_url() ?>';
                }, 2000);
            }
        }
    });
    
    // Initialize DataTable
    const table = $('#applicationsTable').DataTable({
        responsive: true,
        order: [[3, 'desc']], // Sort by date applied desc
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications available for pre-screening",
        }
    });
    
    // Handle select all checkbox
    $('#selectAll').change(function() {
        const isChecked = $(this).prop('checked');
        $('.app-checkbox').prop('checked', isChecked);
        updateBatchButton();
    });
    
    // Handle individual checkboxes
    $(document).on('change', '.app-checkbox', function() {
        updateBatchButton();
        
        // If any checkbox is unchecked, uncheck the "select all" checkbox
        if (!$(this).prop('checked')) {
            $('#selectAll').prop('checked', false);
        }
        
        // If all checkboxes are checked, check the "select all" checkbox
        const totalCheckboxes = $('.app-checkbox').length;
        const checkedCheckboxes = $('.app-checkbox:checked').length;
        if (totalCheckboxes === checkedCheckboxes) {
            $('#selectAll').prop('checked', true);
        }
    });
    
    // Update batch pre-screen button state
    function updateBatchButton() {
        const selectedCount = $('.app-checkbox:checked').length;
        $('#batchPreScreenBtn').prop('disabled', selectedCount === 0)
            .text(selectedCount > 0 ? `Pre-Screen Selected (${selectedCount})` : 'Pre-Screen Selected');
    }
    
    // Handle pre-screen button click (individual)
    $(document).on('click', '.pre-screen-btn', function() {
        const id = $(this).data('id');
        $('#applicationId').val(id);
        $('#preScreenForm')[0].reset();
        $('#preScreenModal').modal('show');
    });
    
    // Handle batch pre-screen button click
    $('#batchPreScreenBtn').click(function() {
        $('#batchPreScreenForm')[0].reset();
        $('#batchPreScreenModal').modal('show');
    });
    
    // Handle save pre-screen button click (single application)
    $('#savePreScreenBtn').click(function() {
        const id = $('#applicationId').val();
        const status = $('#status').val();
        const remarks = $('#remarks').val();
        
        // Validate form
        if (!status) {
            toastr.error('Please select a pre-screening status');
            return;
        }
        
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';
        
        // Create data object
        const data = {
            status: status,
            remarks: remarks
        };
        data[csrfName] = csrfToken;
        
        // Submit pre-screen results
        $.ajax({
            url: `<?= base_url('application_pre_screening/pre_screen') ?>/${id}`,
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function() {
                // Disable buttons to prevent double submission
                $('#savePreScreenBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                    
                    // Close the modal
                    $('#preScreenModal').modal('hide');
                    
                    // Remove the row from the table
                    table.row($(`.app-checkbox[value="${id}"]`).closest('tr')).remove().draw();
                    
                    // Update UI as needed
                    updateBatchButton();
                } else {
                    // Show error message
                    toastr.error(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');
                
                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }
            },
            complete: function() {
                // Re-enable button
                $('#savePreScreenBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Pre-Screening Result');
            }
        });
    });
    
    // Handle save batch pre-screen button click
    $('#saveBatchPreScreenBtn').click(function() {
        const status = $('#batchStatus').val();
        const remarks = $('#batchRemarks').val();
        
        // Validate form
        if (!status) {
            toastr.error('Please select a pre-screening status');
            return;
        }
        
        // Get selected application IDs
        const selectedIds = [];
        $('.app-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';
        
        // Create data object
        const data = {
            ids: selectedIds,
            status: status,
            remarks: remarks
        };
        data[csrfName] = csrfToken;
        
        // Submit batch pre-screen results
        $.ajax({
            url: '<?= base_url('application_pre_screening/batch_pre_screen') ?>',
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function() {
                // Disable button to prevent double submission
                $('#saveBatchPreScreenBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                    
                    // Close the modal
                    $('#batchPreScreenModal').modal('hide');
                    
                    // Remove the pre-screened applications from the table
                    selectedIds.forEach(function(id) {
                        table.row($(`.app-checkbox[value="${id}"]`).closest('tr')).remove().draw();
                    });
                    
                    // Uncheck select all checkbox
                    $('#selectAll').prop('checked', false);
                    
                    // Update UI as needed
                    updateBatchButton();
                } else {
                    // Show error message
                    toastr.error(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');
                
                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }
            },
            complete: function() {
                // Re-enable button
                $('#saveBatchPreScreenBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Pre-Screening Results');
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 