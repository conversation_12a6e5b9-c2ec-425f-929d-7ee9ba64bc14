<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Back Button -->
    <div class="mb-3">
        <a href="<?= base_url('applicant/applications') ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Back to Applications
        </a>
    </div>
    
    <!-- Application Header -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-3">
                        <h3 class="card-title mb-0 me-3">Application #<?= esc($application['application_number']) ?></h3>
                        <?php
                        $statusBadgeClass = 'bg-secondary';
                        switch ($application['status'] ?? '') {
                            case 'submitted':
                                $statusBadgeClass = 'bg-info';
                                break;
                            case 'under_review':
                                $statusBadgeClass = 'bg-primary';
                                break;
                            case 'shortlisted':
                                $statusBadgeClass = 'bg-warning';
                                break;
                            case 'selected':
                                $statusBadgeClass = 'bg-success';
                                break;
                            case 'rejected':
                                $statusBadgeClass = 'bg-danger';
                                break;
                        }
                        ?>
                        <span class="badge <?= $statusBadgeClass ?>">
                            <?= ucwords(str_replace('_', ' ', $application['status'] ?? 'Pending')) ?>
                        </span>
                    </div>
                    <h4 class="text-navy">
                        <?= esc($position['designation'] ?? 'Position Not Found') ?>
                    </h4>
                    <?php if (!empty($organization)): ?>
                    <p class="text-muted mb-2">
                        <i class="fas fa-building me-2"></i><?= esc($organization['name'] ?? '') ?>
                    </p>
                    <?php endif; ?>
                    <?php if (!empty($position['location'])): ?>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-2"></i><?= esc($position['location']) ?>
                    </p>
                    <?php endif; ?>
                    <p class="mb-2">
                        <span class="badge bg-secondary me-2">Reference: <?= esc($position['position_reference'] ?? 'N/A') ?></span>
                        <?php if (!empty($position['classification'])): ?>
                        <span class="badge bg-secondary me-2">Classification: <?= esc($position['classification']) ?></span>
                        <?php endif; ?>
                        <?php if (!empty($position['award'])): ?>
                        <span class="badge bg-secondary">Award: <?= esc($position['award']) ?></span>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light h-100">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">Application Details</h6>
                            <p class="mb-2">
                                <strong>Applied:</strong> <?= date('d M Y', strtotime($application['created_at'])) ?>
                            </p>
                            <?php if (!empty($exercise)): ?>
                            <p class="mb-2">
                                <strong>Exercise:</strong> #<?= esc($exercise['gazzetted_no'] ?? '') ?>
                            </p>
                            <?php endif; ?>
                            <?php if (!empty($position['annual_salary'])): ?>
                            <p class="mb-0">
                                <strong>Annual Salary:</strong> K<?= number_format((float)$position['annual_salary'], 2) ?>
                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Details Section -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Job Details</h5>
            <?php if (!empty($position['jd_filepath'])): ?>
            <a href="<?= base_url(str_replace('public/', '', $position['jd_filepath'])) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                <i class="fas fa-file-pdf me-2"></i>View Job Description
            </a>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Qualifications -->
                <?php if (!empty($position['qualifications'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Qualifications</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($position['qualifications'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Knowledge -->
                <?php if (!empty($position['knowledge'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Knowledge</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($position['knowledge'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Skills & Competencies -->
                <?php if (!empty($position['skills_competencies'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Skills & Competencies</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($position['skills_competencies'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Experience -->
                <?php if (!empty($position['job_experiences'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Experience</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($position['job_experiences'])) ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Applicant Information -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Full Name</label>
                    <p><?= esc($application['fname'] . ' ' . $application['lname']) ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Gender</label>
                    <p><?= esc($application['gender'] ?? 'Not specified') ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Date of Birth</label>
                    <p><?= !empty($application['dobirth']) ? date('d M Y', strtotime($application['dobirth'])) : 'Not specified' ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Place of Origin</label>
                    <p><?= esc($application['place_of_origin'] ?? 'Not specified') ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Citizenship</label>
                    <p><?= esc($application['citizenship'] ?? 'Not specified') ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Marital Status</label>
                    <p><?= esc(ucfirst($application['marital_status'] ?? 'Not specified')) ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Current Employer</label>
                    <p><?= esc($application['current_employer'] ?? 'Not specified') ?></p>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="fw-bold">Current Position</label>
                    <p><?= esc($application['current_position'] ?? 'Not specified') ?></p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="fw-bold">Contact Details</label>
                    <p><?= nl2br(esc($application['contact_details'] ?? 'Not specified')) ?></p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="fw-bold">Location/Address</label>
                    <p><?= nl2br(esc($application['location_address'] ?? 'Not specified')) ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Education Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Education</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($education)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Institution</th>
                                <th>Course</th>
                                <th>Level</th>
                                <th>Period</th>
                                <th>Units/Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($education as $edu): ?>
                                <tr>
                                    <td><?= esc($edu['institution']) ?></td>
                                    <td><?= esc($edu['course']) ?></td>
                                    <td>
                                        <?php 
                                        $levelId = $edu['education_level'] ?? 0;
                                        echo $educationLevels[$levelId] ?? 'Not specified';
                                        ?>
                                    </td>
                                    <td>
                                        <?= date('M Y', strtotime($edu['date_from'])) ?> - 
                                        <?= !empty($edu['date_to']) ? date('M Y', strtotime($edu['date_to'])) : 'Present' ?>
                                    </td>
                                    <td><?= esc($edu['units'] ?? 'N/A') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No education records provided.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Work Experience Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Work Experience</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($experiences)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Employer</th>
                                <th>Position</th>
                                <th>Period</th>
                                <th>Description</th>
                                <th>Achievements</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($experiences as $exp): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($exp['employer']) ?></strong>
                                        <?php if (!empty($exp['employer_contacts_address'])): ?>
                                            <div class="small text-muted mt-1"><?= nl2br(esc($exp['employer_contacts_address'])) ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= esc($exp['position']) ?></td>
                                    <td>
                                        <?= date('M Y', strtotime($exp['date_from'])) ?> - 
                                        <?= !empty($exp['date_to']) ? date('M Y', strtotime($exp['date_to'])) : 'Present' ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($exp['work_description'])): ?>
                                            <div class="small"><?= nl2br(esc($exp['work_description'])) ?></div>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($exp['achievements'])): ?>
                                            <div class="small"><?= nl2br(esc($exp['achievements'])) ?></div>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <p class="mb-0"><strong>Total Experience:</strong> <?= number_format($totalExperience, 1) ?> years</p>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No work experience records provided.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Attached Files Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-paperclip me-2"></i>Attached Documents</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($files)): ?>
                <div class="row">
                    <?php foreach ($files as $file): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title"><?= esc($file['file_title']) ?></h6>
                                    <?php if (!empty($file['file_description'])): ?>
                                        <p class="card-text small text-muted"><?= esc($file['file_description']) ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <a href="<?= base_url(str_replace('public/', '', $file['file_path'])) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-download me-2"></i>Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No files attached to this application.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Additional Information Section -->
    <?php if (
        !empty($application['referees']) || 
        !empty($application['offence_convicted']) || 
        !empty($application['publications']) || 
        !empty($application['awards']) ||
        !empty($application['how_did_you_hear_about_us'])
    ): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Additional Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php if (!empty($application['referees'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Referees</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($application['referees'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($application['offence_convicted'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Convictions</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($application['offence_convicted'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($application['publications'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Publications</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($application['publications'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($application['awards'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">Awards & Recognition</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($application['awards'])) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($application['how_did_you_hear_about_us'])): ?>
                <div class="col-md-6 mb-4">
                    <h6 class="fw-bold">How did you hear about this opportunity?</h6>
                    <div class="p-3 bg-light rounded">
                        <?= nl2br(esc($application['how_did_you_hear_about_us'])) ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Remarks Section (if any) -->
    <?php if (!empty($application['remarks'])): ?>
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-comment me-2"></i>Remarks from Recruiter</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <?= nl2br(esc($application['remarks'])) ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Print Button -->
    <div class="mb-4 text-center">
        <button type="button" class="btn btn-primary" onclick="window.print();">
            <i class="fas fa-print me-2"></i>Print Application
        </button>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize any scripts needed for the page
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 