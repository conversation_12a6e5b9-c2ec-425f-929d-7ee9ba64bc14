<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Provinces</h5>
                    <button type="button" class="btn btn-light" data-toggle="modal" data-target="#addProvinceModal">
                        <i class="fas fa-plus"></i> Add Province
                    </button>
                </div>
                <div class="card-body">
                    <table class="table table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>#</th>
                                <th>Prov. Code</th>
                                <th>JSON ID</th>
                                <th>Name</th>
                                <th>Districts</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($provinces as $province): ?>
                                <tr>
                                    <td><?= $i++ ?></td>
                                    <td><?= esc($province['provincecode']) ?></td>
                                    <td><?= esc($province['json_id']) ?></td>
                                    <td><?= esc($province['name']) ?></td>
                                    <td>
                                        <a href="<?= base_url('dakoii/district/list/' . $province['id']) ?>"
                                            class="btn btn-sm btn-info">
                                            <i class="fas fa-list"></i> Manage Districts
                                        </a>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-province"
                                            data-id="<?= $province['id'] ?>"
                                            data-name="<?= esc($province['name']) ?>"
                                            data-code="<?= esc($province['provincecode']) ?>"
                                            data-toggle="modal"
                                            data-target="#editProvinceModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="<?= base_url('dakoii/province/delete/' . $province['id']) ?>"
                                            class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure you want to delete the province <?= esc($province['name']) ?>? This action cannot be undone.')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Province JSON Data</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>FID</th>
                                            <th>Province ID</th>
                                            <th>Province Name</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $json_file = file_get_contents(base_url(JSON_MAP_PROVINCE));
                                        $data = json_decode($json_file, true);
                                        
                                        foreach ($data['features'] as $feature) {
                                            $properties = $feature['properties'];
                                            ?>
                                            <tr>
                                                <td><?= esc($properties['FID']) ?></td>
                                                <td><?= esc($properties['PROVID']) ?></td>
                                                <td><?= esc($properties['PROVNAME']) ?></td>
                                            </tr>
                                            <?php
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Province Modal -->
<div class="modal fade" id="addProvinceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Add New Province</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dakoii/province/create') ?>
            <div class="modal-body">
                <input type="hidden" name="country_id" value="<?= $set_country['id'] ?>">

                <div class="form-group">
                    <label>Province Code</label>
                    <input type="text" class="form-control" name="provincecode" required>
                </div>

                <div class="form-group">
                    <label>Province Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
                
                <div class="form-group">
                    <label>JSON ID</label>
                    <input type="text" class="form-control" name="json_id" required>
                </div>
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save Province</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Province Modal -->
<div class="modal fade" id="editProvinceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Edit Province</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dakoii/province/update') ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">

                <div class="form-group">
                    <label>Province Code</label>
                    <input type="text" class="form-control" name="provincecode" id="edit_provincecode" required>
                </div>

                <div class="form-group">
                    <label>Province Name</label>
                    <input type="text" class="form-control" name="name" id="edit_name" required>
                </div>
                
                <div class="form-group">
                    <label>JSON ID</label>
                    <input type="text" class="form-control" name="json_id" id="edit_json_id" required>
                </div>
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update Province</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle edit province button clicks
        document.querySelectorAll('.edit-province').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;
                const name = this.dataset.name;
                const code = this.dataset.code;
                const json_id = this.dataset.json_id;
                document.getElementById('edit_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_provincecode').value = code;
                document.getElementById('edit_json_id').value = json_id;
            });
        });
    });
</script>

<?= $this->endSection() ?>