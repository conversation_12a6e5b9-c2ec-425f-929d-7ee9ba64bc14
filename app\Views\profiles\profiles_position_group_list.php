<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= esc($exercise['exercise_name'] ?? 'Exercise Details') ?></li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Position Groups') ?></h1>
            <a href="<?= base_url('profile_applications_exercise') ?>" class="btn btn-sm btn-secondary">Back to Exercises</a>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <p class="mb-4">Select a position group to view its associated positions for profiling.</p>

            <?php if (!empty($position_groups) && is_array($position_groups)): ?>
                <div class="list-group">
                    <?php foreach ($position_groups as $group): ?>
                        <a href="<?= base_url('profile_applications_exercise/profile_group/' . esc($group['id'], 'url')) ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <span>
                                <i class="fas fa-layer-group me-2 text-primary"></i>
                                <?= esc($group['group_name'] ?? 'Unnamed Group') ?>
                                <div class="mt-1 small">
                                    <span class="badge bg-info rounded-pill"><?= esc($group['position_count'] ?? 0) ?> Positions</span>
                                    <span class="badge bg-success rounded-pill"><?= esc($group['applicant_count'] ?? 0) ?> Applications</span>
                                </div>
                            </span>
                            <span class="badge bg-primary rounded-pill">View Positions <i class="fas fa-arrow-right ms-1"></i></span>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info" role="alert">
                    No position groups found for this exercise.
                     <br><small>Note: Position Group data fetching is currently placeholder in the controller.</small>
                </div>
                 <!-- Placeholder for demonstration -->
                 <div class="list-group">
                    <a href="<?= base_url('profile_applications_exercise/profile_group/1') ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-layer-group me-2 text-primary"></i>
                            Sample Group 1 (Placeholder)
                        </span>
                        <span class="badge bg-primary rounded-pill">View Positions <i class="fas fa-arrow-right ms-1"></i></span>
                    </a>
                 </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Add any specific JS for this page if needed -->
<?= $this->endSection() ?>