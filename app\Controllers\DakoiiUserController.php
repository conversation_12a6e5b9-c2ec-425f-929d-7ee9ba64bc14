<?php

namespace App\Controllers;

class DakoiiUserController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    // User Management Methods
    public function systemUserCreateForm()
    {
        $data['title'] = "Create System User";
        $data['menu'] = "users";
        echo view('dakoii/dakoii_system_user_create', $data);
    }

    public function systemUserStore()
    {
        if (!$this->validate([
            'username' => 'required|is_unique[dakoii_users.username]',
            'password' => 'required'
        ])) {
            session()->setFlashdata('error', 'Username already exist or validation failed');
            return redirect()->to('dakoii/dashboard');
        }

        $is_active = !empty($this->request->getVar('is_active')) ? $this->request->getVar('is_active') : "0";

        $data = [
            'name' => $this->request->getVar('name'),
            'username' => $this->request->getVar('username'),
            'password' => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT),
            'role' => $this->request->getVar('role'),
            'is_active' => $is_active,
        ];

        $this->dusersModel->insert($data);
        session()->setFlashdata('success', 'Admin Created');
        return redirect()->to('dakoii/dashboard');
    }

    public function organizationAdminCreateForm($orgcode)
    {
        $org = $this->orgModel->where('org_code', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->to('dakoii/dashboard');
        }

        $data['title'] = "Create Organization Admin";
        $data['menu'] = "organizations";
        $data['org'] = $org;
        echo view('dakoii/dakoii_organization_admin_create', $data);
    }

    public function organizationAdminStore()
    {
        $orgcode = $this->request->getVar('orgcode');
        if (!$this->validate([
            'username' => 'required|is_unique[users.username]',
            'password' => 'required'
        ])) {
            session()->setFlashdata('error', 'Username already taken or validation failed');
            return redirect()->to('dakoii/organization/view/'.$orgcode);
        }

        $status = !empty($this->request->getVar('is_active')) ? 1 : 0;

        // Get organization details
        $org = $this->orgModel->where('org_code', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->back();
        }

        $data = [
            'org_id' => $org['id'], // Set org_id from the organization
            'orgcode' => $orgcode,
            'name' => $this->request->getVar('name'),
            'username' => $this->request->getVar('username'),
            'password' => $this->request->getVar('password'),
            'role' => $this->request->getVar('role'),
            'status' => $status,
            'created_by' => session()->get('user_id'), // Add created_by from session
            'updated_by' => session()->get('user_id')  // Add updated_by from session
        ];

        $this->usersModel->insert($data);
        session()->setFlashdata('success', 'Organization Admin Created');
        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    public function organizationAdminUpdate()
    {
        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        // Get organization details
        $org = $this->orgModel->where('org_code', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->back();
        }

        $rules = [
            'name' => 'required',
            'username' => 'required|is_unique[users.username,id,' . $id . ']',
            'role' => 'required'
        ];

        if ($this->validate($rules)) {
            $data = [
                'org_id' => $org['id'], // Set org_id from the organization
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'role' => $this->request->getVar('role'),
                'status' => $this->request->getVar('is_active') ? 1 : 0,
                'updated_by' => session()->get('user_id')  // Add updated_by from session
            ];

            // Handle password update
            $password = $this->request->getVar('password');
            if (!empty($password)) {
                $data['password'] = $password;
            }

            if ($this->usersModel->update($id, $data)) {
                session()->setFlashdata('success', 'Admin updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update admin');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function systemUserUpdate()
    {
        $id = $this->request->getVar('id');

        $rules = [
            'name' => 'required',
            'username' => 'required|is_unique[dakoii_users.username,id,' . $id . ']',
            'role' => 'required'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'username' => $this->request->getPost('username'),
                'role' => $this->request->getPost('role'),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0
            ];

            // Handle password update
            $password = $this->request->getPost('password');
            if (!empty($password)) {
                $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
            }

            if ($this->dusersModel->update($id, $data)) {
                session()->setFlashdata('success', 'System user updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update system user');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }
}
