<?php

namespace App\Controllers;

class UserSettingsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();

        // Load helpers
        helper(['url', 'form', 'GeminiAI']);
    }

    /**
     * Display the full profile of an applicant/employee with comprehensive information from all models.
     * @param int $applicationId The application ID to view
     */
    public function profileViewEmployee($applicationId)
    {
        // Fetch the application information with joined position data
        $application = $this->applicationModel
            ->select('appx_application_information.*, positions.designation, positions.position_group_id, positions.id as position_id')
            ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
            ->find($applicationId);

        if (!$application) {
            session()->setFlashdata('error', 'Application not found.');
            return redirect()->to(base_url('profile_applications_exercise'));
        }

        // Get position details
        $position = $this->positionModel->find($application['position_id']);

        // Handle case where position is not found
        if (empty($position)) {
            $position = [
                'id' => $application['position_id'],
                'designation' => $application['designation'] ?? 'Unknown Position'
            ];
        }

        // Get position group and exercise details for breadcrumbs
        if (isset($position['position_group_id'])) {
            $group = $this->positionsGroupModel->find($position['position_group_id']);
            if ($group) {
                $position['group_name'] = $group['group_name'] ?? 'Group ' . $group['id'];

                if (isset($group['exercise_id'])) {
                    $exercise = $this->exerciseModel->find($group['exercise_id']);
                    if ($exercise) {
                        $position['exercise_name'] = $exercise['exercise_name'];
                        $position['exercise_id'] = $exercise['id'];
                    } else {
                        $position['exercise_name'] = 'Exercise ' . $group['exercise_id'];
                        $position['exercise_id'] = $group['exercise_id'];
                    }
                }
            }
        }

        // Fetch exercise details
        $exerciseData = null;
        if (isset($position['exercise_id'])) {
            $exerciseData = $this->exerciseModel->find($position['exercise_id']);
        }

        // Get position group details
        $positionGroup = null;
        if (isset($position['position_group_id'])) {
            $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);
        }

        // Parse the profile details JSON if it exists
        $profileDetails = [];
        if (!empty($application['profile_details'])) {
            try {
                $profileDetails = json_decode($application['profile_details'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $profileDetails = []; // Reset if invalid JSON
                    log_message('error', 'Invalid JSON in profile_details for application ID: ' . $applicationId);
                }
            } catch (\Exception $e) {
                log_message('error', 'Error parsing profile_details JSON: ' . $e->getMessage());
            }
        }

        // Fetch education records
        $education = $this->educationModel->getEducationByApplicationId($applicationId);

        // Fetch experience records and calculate summary statistics
        $experiences = $this->experienceModel->getExperiencesByApplicationId($applicationId);
        $experienceSummary = $this->experienceModel->getExperiencesSummary($applicationId);

        // Fetch uploaded files
        $files = $this->filesModel->getFilesByApplicationId($applicationId);

        // If user who profiled the application is known, fetch their username
        $profiledByName = '';
        if (!empty($application['profiled_by'])) {
            // You may need to add a Users model for this
            // $userModel = new \App\Models\UserModel();
            // $user = $userModel->find($application['profiled_by']);
            // if ($user) {
            //     $profiledByName = $user['username'] ?? $user['name'] ?? '';
            // }
        }

        $data = [
            'title' => 'Employee Profile: ' . $application['fname'] . ' ' . $application['lname'],
            'menu' => 'profiling',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exerciseData,
            'education' => $education,
            'experiences' => $experiences,
            'experienceSummary' => $experienceSummary,
            'files' => $files,
            'educationModel' => $this->educationModel,
            'profiledByName' => $profiledByName,
            'profileDetails' => $profileDetails
        ];

        return view('profiles/profile_employee_view', $data);
    }

    /**
     * Update the profile status of an application
     * @param int $applicationId The application ID to update
     */
    public function profileUpdateStatus($applicationId)
    {
        // Validate that the application exists
        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            session()->setFlashdata('error', 'Application not found.');
            return redirect()->to(base_url('profile_applications_exercise'));
        }

        // Validate input
        $rules = [
            'profile_status' => 'required|in_list[pending,in_progress,completed]',
            'profile_details' => 'permit_empty|string'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Invalid profile status selection.');
            return redirect()->back()->withInput();
        }

        // Prepare update data
        $data = [
            'profile_status' => $this->request->getPost('profile_status'),
            'profile_details' => $this->request->getPost('profile_details'),
            'updated_by' => $this->session->get('user_id')
        ];

        // If status is being updated to 'completed', set the profiled_at and profiled_by fields
        if ($data['profile_status'] == 'completed' &&
            ($application['profile_status'] != 'completed' || empty($application['profiled_at']))) {
            $data['profiled_at'] = date('Y-m-d H:i:s');
            $data['profiled_by'] = $this->session->get('user_id');
        }

        // Update the application
        if ($this->applicationModel->update($applicationId, $data)) {
            session()->setFlashdata('message', 'Profile status updated successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to update profile status: ' . implode(', ', $this->applicationModel->errors()));
        }

        return redirect()->to(base_url('profile_applications_exercise/profile_view_employee/' . $applicationId));
    }

    /**
     * Generate a profile for an employee based on their application data
     * @param int $applicationId The application ID to generate profile for
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function generate_profile($applicationId)
    {
        // Check if AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        // CSRF verification is automatic in CodeIgniter 4 for all POST requests
        // The AJAX call must include the CSRF token in either the request headers or form data

        // Load Gemini AI helper
        helper('GeminiAI');

        // Fetch the application
        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Application not found'
            ]);
        }

        // Get position details
        $position = $this->positionModel->find($application['position_id']);

        // Get position group and exercise details
        $positionGroup = null;
        $exercise = null;

        if (!empty($position['position_group_id'])) {
            $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);
            if (!empty($positionGroup['exercise_id'])) {
                $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);
            }
        }

        // Fetch education records
        $education = $this->educationModel->getEducationByApplicationId($applicationId);

        // Fetch experience records and summary
        $experiences = $this->experienceModel->getExperiencesByApplicationId($applicationId);
        $experienceSummary = $this->experienceModel->getExperiencesSummary($applicationId);

        // Fetch files
        $files = $this->filesModel->getFilesByApplicationId($applicationId);

        // Prepare comprehensive data for Gemini AI
        $profileData = [
            // I. Core Identifiers & Contact Information
            'core_identifiers' => [
                'name' => $application['fname'] . ' ' . $application['lname'],
                'date_of_birth' => !empty($application['dobirth']) ? date('Y-m-d', strtotime($application['dobirth'])) : 'Not specified',
                'age' => !empty($application['dobirth']) ? $this->calculateAge($application['dobirth']) : 'Not specified',
                'sex' => $application['gender'] ?? 'Not specified',
                'address_location' => $application['location_address'] ?? 'Not specified',
                'contact_details' => $application['contact_details'] ?? 'Not specified',
                'place_of_origin' => $application['place_of_origin'] ?? 'Not specified',
                'nid_number' => $application['id_numbers'] ?? 'Not specified',
                'citizenship' => $application['citizenship'] ?? 'Not specified'
            ],

            // II. Employment Information
            'employment_information' => [
                'current_position' => $application['current_position'] ?? 'Not specified',
                'current_employer' => $application['current_employer'] ?? 'Not specified',
                'current_salary' => $application['current_salary'] ?? 'Not specified',
                'public_service_status' => isset($application['current_employer']) &&
                                          stripos($application['current_employer'], 'government') !== false ?
                                          'Current public servant' : 'Status not confirmed'
            ],

            // III. Education & Training
            'education_training' => [
                'qualifications' => [], // Will be filled below with education data
                'position_applied_for' => [
                    'title' => $position['designation'] ?? 'Unknown Position',
                    'requirements' => $position['requirements'] ?? 'Not specified'
                ],
                'training_courses' => [] // Will be filled below
            ],

            // IV. Knowledge, Skills, and Competencies
            'knowledge_skills' => [
                'knowledge' => [], // Will be derived from education and experience
                'skills_competencies' => [] // Will be derived from experience and achievements
            ],

            // V. Experience
            'experience' => [
                'related_job_experience' => [], // Will be filled below
                'experience_summary' => [] // Will be filled below
            ],

            // VI. Performance & Achievements
            'performance_achievements' => [
                'performance_level' => 'Not available in application data',
                'publications' => [],
                'awards' => []
            ],

            // VII. Verification
            'verification' => [
                'referees' => $application['referees'] ?? 'Not provided'
            ],

            // Application Details (for context)
            'application_details' => [
                'application_number' => $application['application_number'] ?? 'N/A',
                'application_date' => date('Y-m-d', strtotime($application['created_at'])),
                'position_group' => $positionGroup['group_name'] ?? 'Unknown Group',
                'exercise' => $exercise['exercise_name'] ?? 'Unknown Exercise',
                'marital_status' => $application['marital_status'] ?? 'Not specified'
            ]
        ];

        // Add spouse information if available
        if (!empty($application['spouse_employer'])) {
            $profileData['core_identifiers']['spouse_employer'] = $application['spouse_employer'];
        }

        if (!empty($application['children'])) {
            $profileData['core_identifiers']['children'] = $application['children'];
        }

        if (!empty($application['offence_convicted'])) {
            $profileData['core_identifiers']['offense_convicted'] = $application['offence_convicted'];
        }

        if (!empty($application['how_did_you_hear_about_us'])) {
            $profileData['application_details']['how_did_you_hear_about_us'] = $application['how_did_you_hear_about_us'];
        }

        // Format education data
        foreach ($education as $edu) {
            $educationEntry = [
                'level' => $this->educationModel->getEducationLevelText($edu['education_level']),
                'institution' => $edu['institution'],
                'course' => $edu['course'],
                'units' => $edu['units'] ?? 'Not specified',
                'date_from' => date('Y-m-d', strtotime($edu['date_from'])),
                'date_to' => !empty($edu['date_to']) ? date('Y-m-d', strtotime($edu['date_to'])) : 'Present'
            ];

            $profileData['education_training']['qualifications'][] = $educationEntry;

            // Add to knowledge based on education
            $profileData['knowledge_skills']['knowledge'][] = [
                'source' => $educationEntry['level'] . ' in ' . $educationEntry['course'] . ' at ' . $educationEntry['institution'],
                'field' => $educationEntry['course']
            ];
        }

        // Format experience data
        foreach ($experiences as $exp) {
            $experienceEntry = [
                'position' => $exp['position'],
                'employer' => $exp['employer'],
                'employer_contacts_address' => $exp['employer_contacts_address'] ?? 'Not specified',
                'date_from' => date('Y-m-d', strtotime($exp['date_from'])),
                'date_to' => !empty($exp['date_to']) ? date('Y-m-d', strtotime($exp['date_to'])) : 'Present',
                'work_description' => $exp['work_description'] ?? 'Not specified',
                'achievements' => $exp['achievements'] ?? 'Not specified',
                'public_service' => (stripos($exp['employer'], 'government') !== false ||
                                    stripos($exp['employer'], 'ministry') !== false ||
                                    stripos($exp['employer'], 'department') !== false) ? 'Yes' : 'No'
            ];

            $profileData['experience']['related_job_experience'][] = $experienceEntry;

            // Extract skills from work descriptions and achievements
            if (!empty($exp['work_description'])) {
                $profileData['knowledge_skills']['skills_competencies'][] = [
                    'source' => 'Work description at ' . $exp['employer'],
                    'details' => $exp['work_description']
                ];

                // Add to knowledge based on work description
                $profileData['knowledge_skills']['knowledge'][] = [
                    'source' => 'Experience as ' . $exp['position'] . ' at ' . $exp['employer'],
                    'details' => $exp['work_description']
                ];
            }

            if (!empty($exp['achievements'])) {
                $profileData['knowledge_skills']['skills_competencies'][] = [
                    'source' => 'Achievements at ' . $exp['employer'],
                    'details' => $exp['achievements']
                ];

                $profileData['performance_achievements']['achievements'][] = [
                    'role' => $exp['position'],
                    'employer' => $exp['employer'],
                    'details' => $exp['achievements']
                ];
            }
        }

        // Add experience summary if available
        if (!empty($experienceSummary)) {
            $totalYears = 'Not available';
            $totalPositions = 0;
            $earliestExperience = 'Not available';

            // Handle either object or array type
            if (is_object($experienceSummary)) {
                if (isset($experienceSummary->total_years)) {
                    $totalYears = number_format((float)$experienceSummary->total_years, 1);
                }
                if (isset($experienceSummary->total_positions)) {
                    $totalPositions = (int)$experienceSummary->total_positions;
                }
                if (isset($experienceSummary->earliest_experience)) {
                    $earliestExperience = date('Y-m-d', strtotime((string)$experienceSummary->earliest_experience));
                }
            } elseif (is_array($experienceSummary)) {
                if (isset($experienceSummary['total_years'])) {
                    $totalYears = number_format((float)$experienceSummary['total_years'], 1);
                }
                if (isset($experienceSummary['total_positions'])) {
                    $totalPositions = (int)$experienceSummary['total_positions'];
                }
                if (isset($experienceSummary['earliest_experience'])) {
                    $earliestExperience = date('Y-m-d', strtotime((string)$experienceSummary['earliest_experience']));
                }
            }

            $profileData['experience']['experience_summary'] = [
                'total_years' => $totalYears,
                'total_positions' => $totalPositions,
                'earliest_experience' => $earliestExperience
            ];
        }

        // Format files data for training/courses
        foreach ($files as $file) {
            // If the file title contains keywords related to training or courses
            $trainingKeywords = ['training', 'course', 'certificate', 'seminar', 'workshop'];
            $publicationKeywords = ['publication', 'paper', 'research', 'article', 'journal'];
            $isTraining = false;
            $isPublication = false;

            foreach ($trainingKeywords as $keyword) {
                if (stripos($file['file_title'], $keyword) !== false ||
                    (isset($file['file_description']) && stripos($file['file_description'], $keyword) !== false)) {
                    $isTraining = true;
                    break;
                }
            }

            foreach ($publicationKeywords as $keyword) {
                if (stripos($file['file_title'], $keyword) !== false ||
                    (isset($file['file_description']) && stripos($file['file_description'], $keyword) !== false)) {
                    $isPublication = true;
                    break;
                }
            }

            if ($isTraining) {
                $profileData['education_training']['training_courses'][] = [
                    'title' => $file['file_title'],
                    'description' => $file['file_description'] ?? 'No description',
                    'date' => date('Y-m-d', strtotime($file['created_at']))
                ];
            }

            if ($isPublication) {
                $profileData['performance_achievements']['publications'][] = [
                    'title' => $file['file_title'],
                    'description' => $file['file_description'] ?? 'No description',
                    'date' => date('Y-m-d', strtotime($file['created_at']))
                ];
            }
        }

        // Add awards if available as part of achievements
        if (!empty($application['awards'])) {
            $awardsArray = preg_split('/\r\n|\r|\n/', $application['awards']);
            foreach ($awardsArray as $award) {
                $profileData['performance_achievements']['awards'][] = trim($award);
            }
        }

        // Call Gemini AI to generate the profile
        $geminiResponse = gemini_generate_profile($profileData);

        if (!$geminiResponse['success']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => $geminiResponse['message']
            ]);
        }

        // Structure the AI-generated profile for storage
        $aiProfile = $geminiResponse['profile'];

        // Create structured profile data for storage
        $structuredProfileData = [
            'ai_generated_profile' => $aiProfile,
            'profile_data' => $profileData
        ];

        // Convert to JSON
        $profileJson = json_encode($structuredProfileData, JSON_PRETTY_PRINT);

        // Update the application with the profile details
        $updateData = [
            'profile_details' => $profileJson,
            'profile_status' => 'completed',
            'profiled_at' => date('Y-m-d H:i:s'),
            'profiled_by' => $this->session->get('user_id'),
            'updated_by' => $this->session->get('user_id')
        ];

        if ($this->applicationModel->update($applicationId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Profile generated successfully using Gemini AI'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to save AI-generated profile: ' . implode(', ', $this->applicationModel->errors())
            ]);
        }
    }

    /**
     * Calculate age from date of birth
     * @param string $dob Date of birth in Y-m-d format
     * @return int Age in years
     */
    private function calculateAge($dob)
    {
        $dobDate = new \DateTime($dob);
        $now = new \DateTime();
        $diff = $now->diff($dobDate);
        return $diff->y;
    }
}
