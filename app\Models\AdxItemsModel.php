<?php

namespace App\Models;

use CodeIgniter\Model;

class AdxItemsModel extends Model
{
    protected $table         = 'adx_items';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    // Fields that are allowed to be set during insert/update operations
    protected $allowedFields = [
        'item_label',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    // Validation
    protected $validationRules = [
        'item_label' => 'required|max_length[255]'
    ];
    
    protected $validationMessages = [
        'item_label' => [
            'required'    => 'Item label is required',
            'max_length'  => 'Item label cannot exceed 255 characters'
        ]
    ];
    
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    
    /**
     * Get item by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getItemById($id)
    {
        return $this->find($id);
    }
    
    /**
     * Get all active items
     *
     * @return array
     */
    public function getAllItems()
    {
        return $this->findAll();
    }
}
