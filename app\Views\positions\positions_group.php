<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Position Groups</h5>
                    <p class="mb-0 text-muted">Exercise #<?= esc($exercise['gazzetted_no']) ?> - <?= esc($exercise['advertisement_no']) ?></p>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Back to Previous Page
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                        <i class="fas fa-plus me-2"></i>Add New Group
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Exercise Info Alert -->
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading">Exercise Information</h6>
                <div class="row">
                    <div class="col-md-8">
                        <p class="mb-0">
                            <strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?><br>
                            <strong>Published:</strong> <?= date('M d, Y', strtotime($exercise['publish_date_from'])) ?> -
                            <?= date('M d, Y', strtotime($exercise['publish_date_to'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex justify-content-end">
                            <?php
                            // Calculate total positions across all groups
                            $totalPositions = 0;
                            foreach ($positions_groups as $group) {
                                $totalPositions += $group['position_count'];
                            }
                            ?>
                            <div class="text-end">
                                <p class="mb-0">
                                    <strong>Groups:</strong> <span class="badge bg-info"><?= count($positions_groups) ?></span><br>
                                    <strong>Positions:</strong> <span class="badge bg-primary"><?= $totalPositions ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table id="positionsTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Group Name</th>
                            <th>Parent Group</th>
                            <th>Description</th>
                            <th>Positions</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($positions_groups as $position_group) : ?>
                            <tr>
                                <td><?= esc($position_group['group_name']) ?></td>
                                <td><?= esc($position_group['parent_name'] ?? 'None') ?></td>
                                <td><?= esc($position_group['description']) ?></td>
                                <td>
                                    <span class="badge bg-primary"><?= $position_group['position_count'] ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('positions/view_positions/' . $position_group['id']) ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-primary"
                                                onclick="editPosition('<?= $position_group['id'] ?>', '<?= esc($position_group['group_name']) ?>', '<?= esc($position_group['description']) ?>', '<?= $position_group['parent_id'] ?>')"
                                                data-bs-toggle="modal" data-bs-target="#editModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="confirmDelete(<?= $position_group['id'] ?>, '<?= esc($position_group['group_name']) ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Position Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('positions/addPositionGroup') ?>" method="post">
                <?= csrf_field() ?>
                <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="parent_id" class="form-label">Parent Group</label>
                        <select class="form-select" id="parent_id" name="parent_id">
                            <option value="">None</option>
                            <?php foreach ($positions_groups as $parent): ?>
                                <option value="<?= $parent['id'] ?>"><?= esc($parent['group_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="group_name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="group_name" name="group_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Position Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('positions/updatePositionGroup') ?>" method="post">
                <?= csrf_field() ?>
                <input type="hidden" id="edit_id" name="id">
                <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_parent_id" class="form-label">Parent Group</label>
                        <select class="form-select" id="edit_parent_id" name="parent_id">
                            <option value="">None</option>
                            <?php foreach ($positions_groups as $parent): ?>
                                <option value="<?= $parent['id'] ?>"><?= esc($parent['group_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_group_name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="edit_group_name" name="group_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Position Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the position group "<span id="delete_group_name" class="fw-bold"></span>"? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" action="" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    if ($.fn.DataTable) {
        $('#positionsTable').DataTable({
            order: [[0, 'asc']]
        });
    }
});

function editPosition(id, name, description, parentId) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_group_name').value = name;
    document.getElementById('edit_description').value = description;
    document.getElementById('edit_parent_id').value = parentId || '';
}

function confirmDelete(id, groupName) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `<?= base_url('positions/deletePositionGroup/') ?>${id}`;
    document.getElementById('delete_group_name').textContent = groupName;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
