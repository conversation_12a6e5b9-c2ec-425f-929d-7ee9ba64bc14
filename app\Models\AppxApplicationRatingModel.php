<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationRatingModel
 *
 * Model for the appx_application_rating table
 */
class AppxApplicationRatingModel extends Model
{
    protected $table         = 'appx_application_rating';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id',
        'rate_item_id',
        'score_grained',
        'score_set',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'application_id' => 'required|numeric',
        'rate_item_id'   => 'required|numeric',
        'score_grained'  => 'required|numeric',
        'score_set'      => 'required|numeric'
    ];

    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'rate_item_id' => [
            'required' => 'Rate item ID is required',
            'numeric'  => 'Rate item ID must be a number'
        ],
        'score_grained' => [
            'required' => 'Score grained is required',
            'numeric'  => 'Score grained must be a number'
        ],
        'score_set' => [
            'required' => 'Score set is required',
            'numeric'  => 'Score set must be a number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get ratings by application ID
     *
     * @param int $applicationId
     * @return array
     */
    public function getRatingsByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)->findAll();
    }

    /**
     * Get rating by application and rate item
     *
     * @param int $applicationId
     * @param int $rateItemId
     * @return array|null
     */
    public function getRatingByApplicationAndItem($applicationId, $rateItemId)
    {
        return $this->where('application_id', $applicationId)
                    ->where('rate_item_id', $rateItemId)
                    ->first();
    }

    /**
     * Get ratings by rate item ID
     *
     * @param int $rateItemId
     * @return array
     */
    public function getRatingsByRateItemId($rateItemId)
    {
        return $this->where('rate_item_id', $rateItemId)->findAll();
    }

    /**
     * Get total score for application
     *
     * @param int $applicationId
     * @return array
     */
    public function getTotalScoreByApplication($applicationId)
    {
        return $this->select('SUM(score_grained) as total_grained, SUM(score_set) as total_set')
                    ->where('application_id', $applicationId)
                    ->first();
    }

    /**
     * Get average scores by rate item
     *
     * @param int $rateItemId
     * @return array
     */
    public function getAverageScoresByRateItem($rateItemId)
    {
        return $this->select('AVG(score_grained) as avg_grained, AVG(score_set) as avg_set, COUNT(*) as count')
                    ->where('rate_item_id', $rateItemId)
                    ->first();
    }

    /**
     * Update or create rating
     *
     * @param int $applicationId
     * @param int $rateItemId
     * @param int $scoreGrained
     * @param int $scoreSet
     * @param int $userId
     * @return bool
     */
    public function updateOrCreateRating($applicationId, $rateItemId, $scoreGrained, $scoreSet, $userId = null)
    {
        $existing = $this->getRatingByApplicationAndItem($applicationId, $rateItemId);
        
        $data = [
            'application_id' => $applicationId,
            'rate_item_id'   => $rateItemId,
            'score_grained'  => $scoreGrained,
            'score_set'      => $scoreSet
        ];
        
        if ($existing) {
            if ($userId !== null) {
                $data['updated_by'] = $userId;
            }
            return $this->update($existing['id'], $data);
        } else {
            if ($userId !== null) {
                $data['created_by'] = $userId;
            }
            return $this->insert($data);
        }
    }

    /**
     * Delete ratings by application ID
     *
     * @param int $applicationId
     * @param int $deletedBy
     * @return bool
     */
    public function deleteRatingsByApplication($applicationId, $deletedBy = null)
    {
        if ($deletedBy !== null) {
            $this->where('application_id', $applicationId)
                 ->set('deleted_by', $deletedBy);
        }
        
        return $this->where('application_id', $applicationId)->delete(null, true);
    }

    /**
     * Get rating statistics
     *
     * @return array
     */
    public function getRatingStatistics()
    {
        $stats = [];
        
        // Total ratings
        $stats['total'] = $this->countAllResults(false);
        
        // Average scores
        $avgScores = $this->select('AVG(score_grained) as avg_grained, AVG(score_set) as avg_set')
                          ->first();
        
        $stats['average_scores'] = [
            'grained' => round($avgScores['avg_grained'], 2),
            'set'     => round($avgScores['avg_set'], 2)
        ];
        
        // Score distribution
        $scoreDistribution = $this->select('score_set, COUNT(*) as count')
                                  ->groupBy('score_set')
                                  ->orderBy('score_set', 'ASC')
                                  ->findAll();
        
        $stats['score_distribution'] = [];
        foreach ($scoreDistribution as $dist) {
            $stats['score_distribution'][$dist['score_set']] = $dist['count'];
        }
        
        return $stats;
    }

    /**
     * Get top rated applications
     *
     * @param int $limit
     * @return array
     */
    public function getTopRatedApplications($limit = 10)
    {
        return $this->select('application_id, SUM(score_grained) as total_score')
                    ->groupBy('application_id')
                    ->orderBy('total_score', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
}
