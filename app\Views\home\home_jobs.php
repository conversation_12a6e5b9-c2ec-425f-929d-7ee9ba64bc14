<?= $this->extend('templates/home_template') ?>

<?= $this->section('css') ?>
<style>
    /* Page-specific styles for jobs page */
    .position-row {
        transition: all 0.3s ease;
    }

    .position-row:hover {
        background-color: rgba(240, 15, 0, 0.05) !important;
    }

    .table th {
        font-weight: 600;
        color: var(--red);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container py-4">
        <h2 class="mb-4">Available Positions</h2>

        <!-- Simple filter -->
        <div class="mb-4">
            <select class="form-select w-auto" onchange="filterTable(this.value)">
                <option value="">All Organizations</option>
                <?php foreach ($organizations as $org): ?>
                    <option value="<?= esc($org['name']) ?>"><?= esc($org['name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Simple table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Position</th>
                                <th>Organization</th>
                                <th>Location</th>
                                <th>Salary</th>
                                <th>Closing Date</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($positions)): ?>
                                <?php foreach ($positions as $position): ?>
                                    <tr class="position-row" data-org="<?= esc($position['org_name']) ?>">
                                        <td>
                                            <strong class="text-dark"><?= esc($position['designation']) ?></strong>
                                            <div class="small text-muted"><?= esc($position['position_reference']) ?></div>
                                        </td>
                                        <td>
                                            <span class="text-dark"><?= esc($position['org_name']) ?></span>
                                            <div class="small text-muted"><?= esc($position['orgcode']) ?></div>
                                        </td>
                                        <td><?= esc($position['location']) ?></td>
                                        <td><?= esc($position['annual_salary']) ?></td>
                                        <td>
                                            <?php if (!empty($position['publish_date_to'])): ?>
                                                <?= date('d M Y', strtotime($position['publish_date_to'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not specified</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('jobs/view/' . $position['id']) ?>" class="btn btn-primary btn-sm">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center p-4">
                                        <div class="text-muted">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-clipboard-x mb-3" viewBox="0 0 16 16">
                                                <path d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5z"/>
                                                <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5zm4 7.793 1.146-1.147a.5.5 0 1 1 .708.708L8.707 10l1.147 1.146a.5.5 0 0 1-.708.708L8 10.707l-1.146 1.147a.5.5 0 0 1-.708-.708L7.293 10 6.146 8.854a.5.5 0 1 1 .708-.708z"/>
                                            </svg>
                                            <h4 class="text-red">No Positions Available</h4>
                                            <p>Check back later for new government positions.</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function filterTable(org) {
    const rows = document.querySelectorAll('.position-row');

    rows.forEach(row => {
        if (org === '' || row.getAttribute('data-org') === org) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
</script>
<?= $this->endSection() ?>