<?php namespace App\Models;

use CodeIgniter\Model;

class DakoiiUsersModel extends Model
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';

    protected $allowedFields = [
        'name',
        'username',
        'password',
        'orgcode',
        'role',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';

    protected $validationRules = [
        'name'     => 'required|max_length[255]',
        'username' => 'required|max_length[255]|is_unique[dakoii_users.username,id,{id}]',
        'password' => 'required|min_length[6]',
        'orgcode'  => 'required|max_length[500]',
        'role'     => 'required|max_length[100]'
    ];

    protected $validationMessages = [
        'username' => [
            'is_unique' => 'Username already exists'
        ]
    ];

    protected $skipValidation = false;

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (!isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        return $data;
    }

    /**
     * Get user by username
     *
     * @param string $username
     * @return array|null
     */
    public function getUserByUsername($username)
    {
        return $this->where('username', $username)->first();
    }

    /**
     * Get active users
     *
     * @return array
     */
    public function getActiveUsers()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Verify user credentials
     *
     * @param string $username
     * @param string $password
     * @return array|null
     */
    public function verifyUser($username, $password)
    {
        $user = $this->getUserByUsername($username);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return null;
    }

    /**
     * Get users by organization code
     *
     * @param string $orgcode
     * @return array
     */
    public function getUsersByOrgCode($orgcode)
    {
        return $this->where('orgcode', $orgcode)->findAll();
    }

    /**
     * Get users by role
     *
     * @param string $role
     * @return array
     */
    public function getUsersByRole($role)
    {
        return $this->where('role', $role)->findAll();
    }

    /**
     * Activate user
     *
     * @param int $id
     * @param int $updatedBy
     * @return bool
     */
    public function activateUser($id, $updatedBy = null)
    {
        $data = ['is_active' => 1];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Deactivate user
     *
     * @param int $id
     * @param int $updatedBy
     * @return bool
     */
    public function deactivateUser($id, $updatedBy = null)
    {
        $data = ['is_active' => 0];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }
}
