<?php
/**
 * View file for listing position groups in an exercise
 * 
 * @var array $exercise The exercise details
 * @var array $positionGroups List of position groups in this exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('applications_pre_screening_exercise') ?>">Exercises</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($exercise['exercise_name']) ?></li>
                </ol>
            </nav>
            <h2><i class="fas fa-object-group me-2"></i>Position Groups</h2>
            <p class="text-muted">
                Position groups for exercise: <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                <small>Advertisement No: <?= esc($exercise['advertisement_no']) ?> | Gazetted No: <?= esc($exercise['gazzetted_no']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('applications_pre_screening_exercise') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Exercises
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($positionGroups)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No position groups found for this exercise.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionGroupsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="35%">Group Name</th>
                                <th width="15%">Positions</th>
                                <th width="15%">Total Applications</th>
                                <th width="15%">Pending Pre-screening</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positionGroups as $group): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($group['group_name']) ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $group['positions_count'] ?? 0 ?> Positions
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?= $group['applications_count'] ?? 0 ?> Applications
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (($group['pending_count'] ?? 0) > 0): ?>
                                            <span class="badge bg-warning">
                                                <?= $group['pending_count'] ?> Pending
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success">All Complete</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('applications_pre_screening_exercise/group/' . $group['id']) ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-briefcase"></i> View Positions
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionGroupsTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by ID asc
        language: {
            search: "Search position groups:",
            lengthMenu: "Show _MENU_ groups per page",
            info: "Showing _START_ to _END_ of _TOTAL_ position groups",
            emptyTable: "No position groups available for this exercise",
        }
    });
});
</script>
<?= $this->endSection() ?> 