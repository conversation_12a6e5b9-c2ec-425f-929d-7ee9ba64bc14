<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">View Rating</h2>
                    <p class="text-muted mb-0">
                        Applicant: <?= esc($application['fname']) ?> <?= esc($application['lname']) ?> | 
                        Position: <?= esc($position['designation']) ?>
                    </p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/position-groups/' . $exercise['id']) ?>" class="text-decoration-none">
                                <?= esc($exercise['exercise_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/positions/' . $positionGroup['id']) ?>" class="text-decoration-none">
                                <?= esc($positionGroup['group_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/applications/' . $position['id']) ?>" class="text-decoration-none">
                                <?= esc($position['designation']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">View Rating</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Applicant Info Card -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Applicant Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p class="mb-2"><strong>Name:</strong> <?= esc($application['fname']) ?> <?= esc($application['lname']) ?></p>
                    <p class="mb-2"><strong>Gender:</strong> <?= esc($application['gender']) ?></p>
                    <p class="mb-2"><strong>Date of Birth:</strong> <?= date('d M Y', strtotime($application['dobirth'])) ?></p>
                </div>
                <div class="col-md-4">
                    <p class="mb-2"><strong>Current Position:</strong> <?= esc($application['current_position']) ?></p>
                    <p class="mb-2"><strong>Current Employer:</strong> <?= esc($application['current_employer']) ?></p>
                    <p class="mb-2"><strong>Current Salary:</strong> <?= esc($application['current_salary']) ?></p>
                </div>
                <div class="col-md-4">
                    <p class="mb-2"><strong>Application Number:</strong> <?= esc($application['application_number']) ?></p>
                    <p class="mb-2"><strong>Position Applied:</strong> <?= esc($position['designation']) ?></p>
                    <p class="mb-2"><strong>Rating Date:</strong> <?= date('d M Y', strtotime($application['rated_at'])) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Rating Score Summary Card -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Rating Summary</h5>
        </div>
        <div class="card-body">
            <div class="row align-items-center mb-4">
                <div class="col-md-4">
                    <div class="text-center mb-3">
                        <div class="display-4 fw-bold text-primary"><?= $ratingScore['percentage'] ?>%</div>
                        <p class="text-muted mb-0">Overall Score</p>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="d-flex justify-content-between mb-1">
                        <span>Total Score</span>
                        <span class="fw-medium"><?= $ratingScore['total_score'] ?> / <?= $ratingScore['max_score'] ?></span>
                    </div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: <?= $ratingScore['percentage'] ?>%;" 
                             aria-valuenow="<?= $ratingScore['percentage'] ?>" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                    
                    <?php if (!empty($application['rating_remarks'])): ?>
                        <div class="mt-3">
                            <p class="fw-medium mb-1">Remarks:</p>
                            <p class="mb-0"><?= nl2br(esc($application['rating_remarks'])) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <h6 class="fw-bold border-bottom pb-2 mb-3">Rating Details</h6>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th class="text-center">Score</th>
                            <th class="text-center">Maximum</th>
                            <th class="text-center">Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($ratingScore['details'] as $category): ?>
                            <tr>
                                <td><?= $category['label'] ?></td>
                                <td class="text-center"><?= $category['score'] ?></td>
                                <td class="text-center"><?= $category['max'] ?></td>
                                <td class="text-center">
                                    <?php
                                    $percentage = $category['max'] > 0 ? ($category['score'] / $category['max']) * 100 : 0;
                                    echo round($percentage) . '%';
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-end gap-2 mb-4">
        <a href="<?= base_url('rating/applications/' . $position['id']) ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Applications
        </a>
        <a href="<?= base_url('rating/rate/' . $application['id']) ?>" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Rating
        </a>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Rating view loaded');
});
</script>
<?= $this->endSection() ?> 