<?= $this->extend("templates/dakoiitemp"); ?>
<?= $this->section('content'); ?>

<div class="vh-100 d-flex align-items-center justify-content-center" style="background-color: #1e2235;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-4 col-md-6">
                <div class="text-center mb-4">
                    <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii Logo" width="80" height="80">
                    <h3 class="mt-3 text-white fw-bold">Dakoii Administration</h3>
                    <p class="text-muted">Sign in to your account</p>
                </div>
                
                <div class="card shadow-lg border-0" style="background-color: #2a2f45; border-radius: 10px;">
                    <div class="card-body p-4">
                        <?php if (session()->has('error')) : ?>
                            <div class="alert alert-danger d-flex align-items-center" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <div><?= session('error') ?></div>
                            </div>
                        <?php endif; ?>
                        
                        <?= form_open('dakoii/login') ?>
                            <div class="mb-3">
                                <label for="username" class="form-label text-muted">Username</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-transparent text-muted border-end-0">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control bg-transparent border-start-0" 
                                           id="username" name="username" required 
                                           placeholder="Enter your username">
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label text-muted">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-transparent text-muted border-end-0">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control bg-transparent border-start-0" 
                                           id="password" name="password" required 
                                           placeholder="Enter your password">
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary py-2">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </div>
                        <?= form_close() ?>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-muted small">
                        &copy; <?= date('Y') ?> <a href="https://www.dakoiims.com" class="text-decoration-none">Dakoii Systems</a>
                        <br>
                        <?= SYSTEM_NAME ?> <span class="badge bg-secondary"><?= SYSTEM_VERSION ?></span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>