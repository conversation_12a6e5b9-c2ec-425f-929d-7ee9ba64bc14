<?php

namespace App\Controllers;

class DakoiiDashboardController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    // Dashboard Methods
    public function dashboard()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";

        // Using dummy data for UI development
        $data['dusers'] = [
            ['id' => 1, 'name' => 'System Admin', 'username' => 'admin', 'role' => 'admin']
        ];
        $data['admins'] = [
            ['id' => 1, 'name' => '<PERSON>', 'username' => 'admin', 'role' => 'admin']
        ];
        $data['org'] = [
            ['id' => 1, 'org_name' => 'Department of Information Technology', 'org_code' => 'DIT'],
            ['id' => 2, 'org_name' => 'Department of Works', 'org_code' => 'DOW']
        ];
        $data['selections'] = [];

        $data['provinces_count'] = 22;
        $data['districts_count'] = 89;
        $data['llgs_count'] = 326;
        $data['wards_count'] = 6112;

        $data['province_stats'] = $this->getProvinceStats();

        $data['education'] = [
            ['id' => 1, 'name' => 'Primary Education', 'icon' => 'fa-school'],
            ['id' => 2, 'name' => 'Secondary Education', 'icon' => 'fa-graduation-cap']
        ];

        // Using dummy pending exercises data
        $data['pending_exercises'] = [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'status' => 'publish_request',
                'org_id' => 1,
                'org_name' => 'Department of Information Technology'
            ]
        ];

        echo view('dakoii/dakoii_ddash', $data);
    }

    private function getProvinceStats()
    {
        // Using dummy province statistics for UI development
        return [
            1 => [
                'name' => 'National Capital District',
                'districts' => 3,
                'llgs' => 15,
                'wards' => 125
            ],
            2 => [
                'name' => 'Western Province',
                'districts' => 8,
                'llgs' => 45,
                'wards' => 320
            ],
            3 => [
                'name' => 'Southern Highlands',
                'districts' => 12,
                'llgs' => 78,
                'wards' => 456
            ]
        ];
    }
}
