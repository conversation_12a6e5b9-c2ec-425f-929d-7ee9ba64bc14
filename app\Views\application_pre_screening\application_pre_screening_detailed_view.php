<?php
/**
 * Detailed view for application pre-screening
 * Shows application details alongside pre-screening criteria
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-clipboard-check me-2"></i>Application Pre-Screening</h2>
            <p class="text-muted">Review application details and complete pre-screening</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('applications_pre_screening_exercise') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Application Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Status:</strong>
                            <span class="badge <?= ($application['pre_screened_status'] ?? '') === 'passed' ? 'bg-success' :
                                (($application['pre_screened_status'] ?? '') === 'failed' ? 'bg-danger' : 'bg-warning') ?>">
                                <?= ucfirst($application['pre_screened_status'] ?? 'Pending') ?>
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Submitted:</strong>
                            <?= date('M d, Y', strtotime($application['created_at'])) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Position:</strong>
                            <?= esc($position['designation'] ?? 'N/A') ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Exercise:</strong>
                            <?= esc($exercise['exercise_name'] ?? 'N/A') ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Application Details Section (Left side) -->
        <div class="col-lg-8">
            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
                    <span class="badge bg-light text-dark">
                        Application #<?= $application['application_number'] ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 text-center mb-3">
                            <?php if (!empty($application['id_photo_path'])): ?>
                                <img src="<?= base_url('public/' . $application['id_photo_path']) ?>" 
                                     alt="Applicant Photo" class="img-thumbnail mb-2" 
                                     style="max-width: 150px;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center mb-2"
                                     style="width: 150px; height: 150px; margin: 0 auto;">
                                    <i class="fas fa-user fa-4x text-secondary"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <h4 class="mb-1"><?= $application['fname'] . ' ' . $application['lname'] ?></h4>
                            <p class="text-muted mb-2">
                                <i class="fas fa-briefcase me-1"></i>
                                Applied for: <?= esc($position['designation']) ?>
                            </p>

                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Gender:</strong> <?= $application['gender'] ?? 'Not specified' ?></p>
                                    <p class="mb-1"><strong>Date of Birth:</strong> <?= $application['dobirth'] ? date('d M Y', strtotime($application['dobirth'])) : 'Not specified' ?></p>
                                    <p class="mb-1"><strong>Marital Status:</strong> <?= $application['marital_status'] ?? 'Not specified' ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Citizenship:</strong> <?= $application['citizenship'] ?? 'Not specified' ?></p>
                                    <p class="mb-1"><strong>Place of Origin:</strong> <?= $application['place_of_origin'] ?? 'Not specified' ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Contact Information</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            // Extract phone from applicant's contact_details (could be JSON or plain number)
                            $phone = 'Not provided';
                            if (!empty($applicant['contact_details'])) {
                                $decodedContact = json_decode($applicant['contact_details'], true);
                                if (is_array($decodedContact) && isset($decodedContact['phone'])) {
                                    $phone = $decodedContact['phone']; // Found phone in JSON
                                } elseif (!is_array($decodedContact)) {
                                    $phone = $applicant['contact_details']; // Assume it's the phone number directly
                                }
                            }
                            ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Email:</strong> <?= esc($applicant['email'] ?? 'Not provided') ?></p>
                                    <p class="mb-1"><strong>Phone:</strong> <?= esc($phone) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Address:</strong> <?= esc($applicant['location_address'] ?? 'Not provided') ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Employment -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Current Employment</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Current Employer:</strong> <?= $application['current_employer'] ?? 'Not provided' ?></p>
                                    <p class="mb-1"><strong>Current Position:</strong> <?= $application['current_position'] ?? 'Not provided' ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Current Salary:</strong> <?= $application['current_salary'] ?? 'Not provided' ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Education History -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Education History</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($education)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No education history provided.
                        </div>
                    <?php else: ?>
                        <?php foreach ($education as $edu): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <?= esc($edu['institution']) ?>
                                        <span class="badge bg-secondary float-end">
                                            <?= $educationModel->getEducationLevelText($edu['education_level'] ?? 0) ?>
                                        </span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <p class="mb-1"><strong>Course:</strong> <?= esc($edu['course']) ?></p>
                                            <p class="mb-1"><strong>Units:</strong> <?= esc($edu['units'] ?? 'Not specified') ?></p>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <p class="mb-0 text-muted small">
                                                <?= date('M Y', strtotime($edu['date_from'])) ?> -
                                                <?= !empty($edu['date_to']) ? date('M Y', strtotime($edu['date_to'])) : 'Present' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Work Experience -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Work Experience</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($experiences)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No work experience provided.
                        </div>
                    <?php else: ?>
                        <?php foreach ($experiences as $exp): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><?= esc($exp['position']) ?> at <?= esc($exp['employer']) ?></h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <p class="mb-1"><strong>Employer Address:</strong> <?= esc($exp['employer_contacts_address'] ?? 'Not provided') ?></p>
                                            <?php if (!empty($exp['work_description'])): ?>
                                                <p class="mb-1"><strong>Description:</strong> <?= esc($exp['work_description']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <p class="mb-0 text-muted small">
                                                <?= date('M Y', strtotime($exp['date_from'])) ?> -
                                                <?= !empty($exp['date_to']) ? date('M Y', strtotime($exp['date_to'])) : 'Present' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Documents -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Documents</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($files)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No document files uploaded.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Document Title</th>
                                        <th>Description</th>
                                        <th>Uploaded</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($files as $file): ?>
                                        <tr>
                                            <td><?= esc($file['file_title']) ?></td>
                                            <td><?= esc($file['file_description'] ?? 'No description') ?></td>
                                            <td><?= date('d M Y', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-primary" target="_blank">
                                                    <i class="fas fa-eye me-1"></i> View
                                                </a>
                                                <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-success" download>
                                                    <i class="fas fa-download me-1"></i> Download
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Pre-Screening Form Section (Right side) -->
        <div class="col-lg-4">
            <!-- Pre-Screening Form -->
            <form id="preScreeningForm" method="post" action="<?= base_url('application_pre_screening/save/' . $application['id']) ?>">
                <?= csrf_field() ?>
                
                <!-- Display validation errors if any -->
                <?php if (session()->has('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <!-- Display success message if any -->
                <?php if (session()->has('success')): ?>
                    <div class="alert alert-success">
                        <?= session('success') ?>
                    </div>
                <?php endif; ?>
                
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-warning d-flex justify-content-between align-items-center py-3">
                        <h5 class="mb-0 text-dark">
                            <i class="fas fa-clipboard-check me-2"></i>Pre-Screening Form
                        </h5>
                        <?php if (!empty($application['pre_screened'])): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Pre-Screened
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <!-- Pre-screening status selection -->
                        <div class="mb-4">
                            <label for="preScreenStatus" class="form-label fw-bold">Pre-Screening Status <span class="text-danger">*</span></label>
                            <select class="form-select form-select-lg" id="preScreenStatus" name="status" required>
                                <option value="">Select Status</option>
                                <option value="passed" <?= ($application['pre_screened_status'] ?? '') === 'passed' ? 'selected' : '' ?>>
                                    Passed
                                </option>
                                <option value="failed" <?= ($application['pre_screened_status'] ?? '') === 'failed' ? 'selected' : '' ?>>
                                    Failed
                                </option>
                                <option value="pending" <?= ($application['pre_screened_status'] ?? '') === 'pending' ? 'selected' : '' ?>>
                                    Pending Additional Info
                                </option>
                            </select>
                        </div>

                        <!-- Pre-screening remarks -->
                        <div class="mb-4">
                            <label for="remarks" class="form-label fw-bold">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4"
                                placeholder="Enter detailed remarks about the pre-screening decision..."><?= $application['pre_screened_remarks'] ?? '' ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Pre-Screening Criteria Checklist -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white py-3">
                        <h5 class="mb-0">
                            <i class="fas fa-tasks me-2"></i>Pre-Screening Criteria
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($preScreenCriteria)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No pre-screening criteria defined for this exercise.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-info-circle me-2"></i> Check each criteria item that the applicant meets.
                            </div>

                            <!-- Progress bar for criteria completion -->
                            <?php
                            // Parse existing results if available
                            $existingResults = [];
                            $metCriteria = 0;
                            $totalCriteria = count($preScreenCriteria);
                            
                            if (!empty($application['pre_screened_criteria_results'])) {
                                $existingResults = json_decode($application['pre_screened_criteria_results'], true) ?? [];
                                foreach ($existingResults as $result) {
                                    if (!empty($result['met'])) {
                                        $metCriteria++;
                                    }
                                }
                            }
                            
                            $percentage = $totalCriteria > 0 ? round(($metCriteria / $totalCriteria) * 100) : 0;
                            ?>
                            
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small text-muted">Criteria Progress</span>
                                    <span class="small text-muted"><?= $metCriteria ?>/<?= $totalCriteria ?> completed</span>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div id="criteriaProgress" class="progress-bar bg-success" role="progressbar"
                                        style="width: <?= $percentage ?>%;" aria-valuenow="<?= $percentage ?>"
                                        aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>

                            <!-- Criteria list -->
                            <div id="criteriaList">
                                <?php foreach ($preScreenCriteria as $index => $criteria): ?>
                                    <?php
                                    // Check if we have a result for this criteria
                                    $isChecked = false;
                                    $criteriaRemarks = '';

                                    if (!empty($existingResults)) {
                                        foreach ($existingResults as $result) {
                                            if (isset($result['criteriaIndex']) && $result['criteriaIndex'] == $index) {
                                                $isChecked = $result['met'] ?? false;
                                                $criteriaRemarks = $result['remarks'] ?? '';
                                                break;
                                            }
                                        }
                                    }
                                    ?>
                                    <div class="card mb-3 criteria-item <?= $isChecked ? 'border-success' : '' ?>">
                                        <div class="card-header bg-light py-3">
                                            <div class="form-check">
                                                <input class="form-check-input criteria-checkbox" type="checkbox"
                                                    id="criteria<?= $index ?>" name="criteria_met[<?= $index ?>]"
                                                    <?= $isChecked ? 'checked' : '' ?>>
                                                <input type="hidden" name="criteria_index[<?= $index ?>]" value="<?= $index ?>">
                                                <label class="form-check-label fw-bold" for="criteria<?= $index ?>">
                                                    <?= esc($criteria['name']) ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <?php if (!empty($criteria['description'])): ?>
                                                <p class="text-muted mb-3"><?= esc($criteria['description']) ?></p>
                                            <?php endif; ?>

                                            <div class="mb-0">
                                                <label for="criteriaRemarks<?= $index ?>" class="form-label small">Remarks</label>
                                                <textarea class="form-control form-control-sm"
                                                    id="criteriaRemarks<?= $index ?>" name="criteria_remarks[<?= $index ?>]"
                                                    rows="2" placeholder="Add specific remarks for this criteria..."><?= esc($criteriaRemarks) ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Submit button -->
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" id="savePreScreeningBtn" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> Save Pre-Screening
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Pre-Screening History Section -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">Pre-Screening History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($application['pre_screened'])): ?>
                                <tr>
                                    <td><?= date('M d, Y H:i', strtotime($application['pre_screened'])) ?></td>
                                    <td>
                                        <span class="badge <?= $application['pre_screened_status'] === 'passed' ? 'bg-success' :
                                            ($application['pre_screened_status'] === 'failed' ? 'bg-danger' : 'bg-warning') ?>">
                                            <?= ucfirst($application['pre_screened_status']) ?>
                                        </span>
                                    </td>
                                    <td><?= nl2br(esc($application['pre_screened_remarks'])) ?></td>
                                </tr>
                                <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center">No pre-screening history available</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add visual feedback when checkboxes are changed
    $('.criteria-checkbox').change(function() {
        const $card = $(this).closest('.criteria-item');
        if ($(this).is(':checked')) {
            $card.addClass('border-success').removeClass('border-danger');
        } else {
            $card.removeClass('border-success');
        }
        
        // Update the progress bar
        updateProgressBar();
    });
    
    // Update the text on the save button based on the selected status
    $('#preScreenStatus').change(function() {
        const status = $(this).val();
        let buttonText = 'Save Pre-Screening';
        
        if (status === 'passed') {
            buttonText = 'Mark as Passed';
        } else if (status === 'failed') {
            buttonText = 'Mark as Failed';
        } else if (status === 'pending') {
            buttonText = 'Mark as Pending';
        }
        
        $('#savePreScreeningBtn').html(`<i class="fas fa-save me-2"></i> ${buttonText}`);
    });
    
    // Function to update the progress bar
    function updateProgressBar() {
        const total = $('.criteria-checkbox').length;
        const checked = $('.criteria-checkbox:checked').length;
        const percentage = (checked / total) * 100;
        
        $('#criteriaProgress').css('width', `${percentage}%`).attr('aria-valuenow', percentage);
        $('.small.text-muted:last').text(`${checked}/${total} completed`);
    }
});
</script>

<style>
/* Styling for criteria items */
.criteria-item {
    transition: all .3s ease-in-out;
}

.criteria-item:hover {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    transform: translateY(-2px);
}

.criteria-checkbox:checked + label {
    color: #198754;
}
</style>
<?= $this->endSection() ?>