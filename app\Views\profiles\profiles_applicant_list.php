<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position['exercise_id'] ?? '#')) ?>"><?= esc($position['exercise_name'] ?? 'Exercise') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_group/' . ($position['position_group_id'] ?? '#')) ?>"><?= esc($position['group_name'] ?? 'Group') ?></a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= esc($position['designation'] ?? 'Position') ?></li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Profiling Applications') ?></h1>
            <a href="<?= base_url('profile_applications_exercise/profile_group/' . ($position['position_group_id'] ?? '#')) ?>" class="btn btn-sm btn-secondary">Back to Positions</a>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <p class="mb-4">Applications listed below have passed pre-screening for the position: <strong><?= esc($position['designation'] ?? 'N/A') ?></strong>. Proceed with profiling actions as needed.</p>

            <?php if (!empty($applications) && is_array($applications)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="profiling-applicants-table" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>App #</th>
                                <th>Name</th>
                                <th>Submitted</th>
                                <th>Acknowledged</th>
                                <th>Pre-Screen Status</th>
                                <th>Profiling Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $app): ?>
                                <tr>
                                    <td><?= esc($app['application_number']) ?></td>
                                    <td><?= esc($app['fname'] . ' ' . $app['lname']) ?></td>
                                    <td><?= esc(date('d M Y H:i', strtotime($app['created_at']))) ?></td>
                                    <td><?= esc($app['recieved_acknowledged'] ? date('d M Y H:i', strtotime($app['recieved_acknowledged'])) : 'N/A') ?></td>
                                    <td>
                                        <span class="badge bg-success"><?= ucfirst(esc($app['pre_screened_status'])) ?></span>
                                    </td>
                                    <td>
                                        <?php 
                                        $profileStatusBadge = 'bg-secondary';
                                        $profileStatusText = 'Not Started';
                                        
                                        if (!empty($app['profile_status'])) {
                                            switch($app['profile_status']) {
                                                case 'completed':
                                                    $profileStatusBadge = 'bg-success';
                                                    $profileStatusText = 'Completed';
                                                    break;
                                                case 'in_progress':
                                                    $profileStatusBadge = 'bg-warning';
                                                    $profileStatusText = 'In Progress';
                                                    break;
                                                case 'pending':
                                                    $profileStatusBadge = 'bg-info';
                                                    $profileStatusText = 'Pending';
                                                    break;
                                                default:
                                                    $profileStatusBadge = 'bg-secondary';
                                                    $profileStatusText = $app['profile_status'] ? ucfirst($app['profile_status']) : 'Not Started';
                                                    break;
                                            }
                                        }
                                        ?>
                                        <span class="badge <?= $profileStatusBadge ?>"><?= $profileStatusText ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('profile_applications_exercise/profile_view_employee/' . $app['id']) ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-user-circle me-1"></i> View Employee Profile
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info" role="alert">
                    No applicants found who passed pre-screening for this position.
                </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Include DataTables if needed -->
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
    $(document).ready(function() {
        $('#profiling-applicants-table').DataTable({
            "order": [[ 2, "desc" ]] // Order by submission date descending by default
        });
    });
</script>
<?= $this->endSection() ?>