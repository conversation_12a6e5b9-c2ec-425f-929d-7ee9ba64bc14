<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Rating - Applications</h2>
                    <p class="text-muted mb-0">View and rate applications for <?= esc($position['designation']) ?></p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/position-groups/' . $exercise['id']) ?>" class="text-decoration-none">
                                <?= esc($exercise['exercise_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/positions/' . $positionGroup['id']) ?>" class="text-decoration-none">
                                <?= esc($positionGroup['group_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page"><?= esc($position['designation']) ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Position Info Card -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Position Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2"><strong>Designation:</strong> <?= esc($position['designation']) ?></p>
                    <p class="mb-2"><strong>Reference:</strong> <?= esc($position['position_reference']) ?></p>
                    <p class="mb-2"><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2"><strong>Location:</strong> <?= esc($position['location']) ?></p>
                    <p class="mb-2"><strong>Annual Salary:</strong> <?= esc($position['annual_salary']) ?></p>
                    <p class="mb-2"><strong>Exercise:</strong> <?= esc($exercise['exercise_name']) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Applications</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="applicationsTable">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Applicant</th>
                            <th>Application Number</th>
                            <th>Pre-Screen Status</th>
                            <th>Profile Status</th>
                            <th>Rating Status</th>
                            <th>Gender</th>
                            <th>Current Position</th>
                            <th>Current Employer</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($applications)): ?>
                            <tr>
                                <td colspan="9" class="text-center py-4">No applications found with applications that have completed profiling</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td class="px-4">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                                <i class="fas fa-user text-muted"></i>
                                            </div>
                                            <span class="fw-medium"><?= esc($application['fname']) ?> <?= esc($application['lname']) ?></span>
                                        </div>
                                    </td>
                                    <td><?= esc($application['application_number']) ?></td>
                                    <td>
                                        <?php if ($application['pre_screened_status'] === 'pass'): ?>
                                            <span class="badge bg-success bg-opacity-10 text-success">Pass</span>
                                        <?php elseif ($application['pre_screened_status'] === 'fail'): ?>
                                            <span class="badge bg-danger bg-opacity-10 text-danger">Fail</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary bg-opacity-10 text-secondary">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($application['profile_status'] === 'completed'): ?>
                                            <span class="badge bg-success bg-opacity-10 text-success">Completed</span>
                                        <?php elseif ($application['profile_status'] === 'in_progress'): ?>
                                            <span class="badge bg-warning bg-opacity-10 text-warning">In Progress</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary bg-opacity-10 text-secondary">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($application['rating_status'] === 'completed'): ?>
                                            <span class="badge bg-success bg-opacity-10 text-success">Rated</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning bg-opacity-10 text-warning">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= esc($application['gender']) ?></td>
                                    <td><?= esc($application['current_position']) ?></td>
                                    <td><?= esc($application['current_employer']) ?></td>
                                    <td>
                                        <?php if ($application['rating_status'] === 'completed'): ?>
                                            <a href="<?= base_url('rating/view/' . $application['id']) ?>" 
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-eye me-1"></i> View Rating
                                            </a>
                                        <?php else: ?>
                                            <a href="<?= base_url('rating/rate/' . $application['id']) ?>" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-star me-1"></i> Rate
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        "order": [], // Disable initial sorting
        "language": {
            "search": "Search applications:",
            "lengthMenu": "Show _MENU_ applications per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ applications",
            "infoEmpty": "Showing 0 to 0 of 0 applications",
            "infoFiltered": "(filtered from _MAX_ total applications)"
        }
    });
});
</script>
<?= $this->endSection() ?>