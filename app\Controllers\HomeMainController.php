<?php

namespace App\Controllers;

class HomeMainController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    public function index()
    {
        // Dummy data for UI development - replace with actual model calls later
        $positions = [
            [
                'id' => 1,
                'designation' => 'Senior Software Engineer',
                'org_name' => 'Department of Information Technology',
                'location' => 'Port Moresby',
                'annual_salary' => '85000',
                'classification' => 'Level 8',
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV-2024-001',
                'publish_date_to' => '2024-12-31'
            ],
            [
                'id' => 2,
                'designation' => 'Project Manager',
                'org_name' => 'Department of Works',
                'location' => 'Lae',
                'annual_salary' => '75000',
                'classification' => 'Level 7',
                'exercise_name' => 'Infrastructure Development Exercise',
                'advertisement_no' => 'ADV-2024-002',
                'publish_date_to' => '2024-11-30'
            ],
            [
                'id' => 3,
                'designation' => 'Financial Analyst',
                'org_name' => 'Department of Treasury',
                'location' => 'Port Moresby',
                'annual_salary' => '65000',
                'classification' => 'Level 6',
                'exercise_name' => 'Finance Recruitment 2024',
                'advertisement_no' => 'ADV-2024-003',
                'publish_date_to' => '2024-12-15'
            ]
        ];

        return view('home/home_home', [
            'title' => 'Home',
            'menu' => 'home',
            'navbar_fixed' => true,
            'latest_positions' => $positions
        ]);
    }

    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
