<?php namespace App\Models;

use CodeIgniter\Model;

/**
 * GeoDistrictsModel
 *
 * Model for the geo_districts table
 */
class GeoDistrictsModel extends Model
{
    protected $table = 'geo_districts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    protected $allowedFields = [
        'district_code',
        'name',
        'country_id',
        'province_id',
        'json_id',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'country_id' => 'required|numeric',
        'province_id' => 'required|numeric',
        'district_code' => 'permit_empty|max_length[10]',
        'json_id' => 'permit_empty|max_length[50]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'District name is required',
            'max_length' => 'District name cannot exceed 100 characters'
        ],
        'country_id' => [
            'required' => 'Country ID is required',
            'numeric' => 'Country ID must be a number'
        ],
        'province_id' => [
            'required' => 'Province ID is required',
            'numeric' => 'Province ID must be a number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get districts by country ID
     *
     * @param int $countryId
     * @return array
     */
    public function getDistrictsByCountry($countryId)
    {
        return $this->where('country_id', $countryId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get districts by province ID
     *
     * @param int $provinceId
     * @return array
     */
    public function getDistrictsByProvince($provinceId)
    {
        return $this->where('province_id', $provinceId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get district by code
     *
     * @param string $code
     * @return array|null
     */
    public function getDistrictByCode($code)
    {
        return $this->where('district_code', $code)->first();
    }

    /**
     * Search districts by name
     *
     * @param string $search
     * @param int $countryId
     * @param int $provinceId
     * @return array
     */
    public function searchDistricts($search, $countryId = null, $provinceId = null)
    {
        $builder = $this->like('name', $search);

        if ($countryId !== null) {
            $builder->where('country_id', $countryId);
        }

        if ($provinceId !== null) {
            $builder->where('province_id', $provinceId);
        }

        return $builder->orderBy('name', 'ASC')->findAll();
    }
}
