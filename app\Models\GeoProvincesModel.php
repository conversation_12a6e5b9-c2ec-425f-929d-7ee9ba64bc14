<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * GeoProvincesModel
 *
 * Model for the geo_provinces table
 */
class GeoProvincesModel extends Model
{
    protected $table = 'geo_provinces';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    protected $allowedFields = [
        'province_code',
        'name',
        'country_id',
        'json_id',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'province_code' => 'required|max_length[10]|is_unique[geo_provinces.province_code,id,{id}]',
        'name' => 'required|max_length[100]',
        'country_id' => 'required|numeric',
        'json_id' => 'permit_empty|max_length[50]'
    ];

    protected $validationMessages = [
        'province_code' => [
            'required' => 'Province code is required',
            'max_length' => 'Province code cannot exceed 10 characters',
            'is_unique' => 'Province code already exists'
        ],
        'name' => [
            'required' => 'Province name is required',
            'max_length' => 'Province name cannot exceed 100 characters'
        ],
        'country_id' => [
            'required' => 'Country ID is required',
            'numeric' => 'Country ID must be a number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get provinces by country ID
     *
     * @param int $countryId
     * @return array
     */
    public function getProvincesByCountry($countryId)
    {
        return $this->where('country_id', $countryId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get province by code
     *
     * @param string $code
     * @return array|null
     */
    public function getProvinceByCode($code)
    {
        return $this->where('province_code', $code)->first();
    }

    /**
     * Search provinces by name
     *
     * @param string $search
     * @param int $countryId
     * @return array
     */
    public function searchProvinces($search, $countryId = null)
    {
        $builder = $this->like('name', $search);

        if ($countryId !== null) {
            $builder->where('country_id', $countryId);
        }

        return $builder->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Get all provinces ordered by name
     *
     * @return array
     */
    public function getAllProvinces()
    {
        return $this->orderBy('name', 'ASC')->findAll();
    }
}
