<?= $this->extend('templates/nolstemp') ?>

<?php
/**
 * Helper function to safely format dates
 * @param string|null $dateStr The date string to format
 * @param string $format The date format to use
 * @param string $defaultText Text to show if date is invalid
 * @return string Formatted date or default text
 */
function safeFormatDate($dateStr, $format = 'd M Y', $defaultText = 'Not specified') {
    if (empty($dateStr)) {
        return $defaultText;
    }
    
    $timestamp = strtotime($dateStr);
    if ($timestamp === false || $timestamp <= 0 || date('Y', $timestamp) <= 0) {
        return $defaultText;
    }
    
    return date($format, $timestamp);
}

/**
 * Helper function to check if a file exists
 * @param string $filePath The file path to check
 * @return bool True if file exists, false otherwise
 */
function fileExists($filePath) {
    // Remove base_url if it's included in the path
    $filePath = str_replace(base_url(), '', $filePath);
    
    // First check with DOCUMENT_ROOT
    $fullPath = $_SERVER['DOCUMENT_ROOT'] . '/' . ltrim($filePath, '/');
    if (file_exists($fullPath)) {
        return true;
    }
    
    // Then check with FCPATH (CodeIgniter's root path)
    if (defined('FCPATH')) {
        $fullPath = FCPATH . ltrim($filePath, '/');
        if (file_exists($fullPath)) {
            return true;
        }
    }
    
    // Log the missing file for debugging purposes
    if (function_exists('log_message')) {
        log_message('error', 'File not found: ' . $filePath . ' (Checked paths: ' 
            . $_SERVER['DOCUMENT_ROOT'] . '/' . ltrim($filePath, '/') 
            . (defined('FCPATH') ? ' and ' . FCPATH . ltrim($filePath, '/') : '') 
            . ')');
    }
    
    return false;
}

/**
 * Simple Markdown to HTML converter for AI-generated content
 * @param string $markdown Markdown text
 * @return string HTML
 */
function simple_markdown_to_html($markdown) {
    // First, escape HTML to prevent XSS
    $markdown = esc($markdown);
    
    // Handle headings (# Heading 1, ## Heading 2, etc.)
    $markdown = preg_replace('/^###### (.*?)$/m', '<h6>$1</h6>', $markdown);
    $markdown = preg_replace('/^##### (.*?)$/m', '<h5>$1</h5>', $markdown);
    $markdown = preg_replace('/^#### (.*?)$/m', '<h4>$1</h4>', $markdown);
    $markdown = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $markdown);
    $markdown = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $markdown);
    $markdown = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $markdown);
    
    // Handle bold and italic
    $markdown = preg_replace('/\*\*(.*?)\*\*/s', '<strong>$1</strong>', $markdown);
    $markdown = preg_replace('/\*(.*?)\*/s', '<em>$1</em>', $markdown);
    
    // Handle sections for structured profile
    $markdown = preg_replace('/^(\d+)\.\s+(.*?)$/m', '<div class="ai-profile-section"><div class="ai-profile-section-title">$2</div>', $markdown);
    
    // Close profile sections
    $markdown = preg_replace('/^(?=\d+\.\s+)/m', '</div>', $markdown);
    
    // Handle unordered lists
    $markdown = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $markdown);
    $markdown = preg_replace('/(?<=\n)<li>/', '<ul><li>', $markdown, 1);
    $markdown = preg_replace('/(?<=\n)(?!\n)<\/li>(?!\n<li>)/', '</li></ul>', $markdown);
    
    // Handle ordered lists
    $markdown = preg_replace('/^\s+\d+\.\s+(.*?)$/m', '<li>$1</li>', $markdown);
    $markdown = preg_replace('/(?<=\n)<li>/', '<ol><li>', $markdown, 1);
    $markdown = preg_replace('/(?<=\n)(?!\n)<\/li>(?!\n<li>)/', '</li></ol>', $markdown);
    
    // Handle paragraphs
    $markdown = preg_replace('/(?<=\n\n)(?!<h|<ul|<li|<ol|<div)(.*?)(?=\n\n)/s', '<p>$1</p>', $markdown);
    
    // Clean up line breaks
    $markdown = str_replace("\n\n", "\n", $markdown);
    $markdown = str_replace("\n", "<br>", $markdown);
    
    // Add the final closing div for the last section
    $markdown .= '</div>';
    
    return $markdown;
}
?>

<?= $this->section('content') ?>

<style>
    /* AI-generated profile styles */
    .ai-profile-content {
        font-size: 1rem;
        line-height: 1.6;
    }
    
    .ai-profile-content h1,
    .ai-profile-content h2,
    .ai-profile-content h3,
    .ai-profile-content h4,
    .ai-profile-content h5,
    .ai-profile-content h6 {
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        font-weight: 600;
        color: #2c3e50;
    }
    
    .ai-profile-content h1 {
        font-size: 1.8rem;
        border-bottom: 2px solid #4e73df;
        padding-bottom: 0.5rem;
    }
    
    .ai-profile-content h2 {
        font-size: 1.5rem;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 0.3rem;
    }
    
    .ai-profile-content h3 {
        font-size: 1.3rem;
    }
    
    .ai-profile-content ul,
    .ai-profile-content ol {
        padding-left: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .ai-profile-content li {
        margin-bottom: 0.5rem;
    }
    
    .ai-profile-content p {
        margin-bottom: 1rem;
    }
    
    .ai-profile-content strong {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .ai-profile-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .ai-profile-section:last-child {
        border-bottom: none;
    }
    
    .ai-profile-section-title {
        font-weight: 600;
        font-size: 1.2rem;
        color: #4e73df;
        margin-bottom: 0.75rem;
    }
    
    .timeline {
        position: relative;
        padding-left: 3rem;
    }
    
    .timeline-item {
        position: relative;
        padding-bottom: 2rem;
    }
    
    .timeline-marker {
        position: absolute;
        left: -3rem;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: white;
    }
    
    .timeline-year {
        margin-left: -3rem;
        margin-bottom: 1rem;
        margin-top: 1rem;
    }
    
    /* Custom hover effect for cards */
    .hover-card {
        transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    }
    
    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
</style>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position['exercise_id'] ?? '#')) ?>"><?= esc($position['exercise_name'] ?? 'Exercise') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_group/' . ($position['position_group_id'] ?? '#')) ?>"><?= esc($position['group_name'] ?? 'Group') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_position/' . ($position['id'] ?? '#')) ?>"><?= esc($position['designation'] ?? 'Position') ?></a></li>
            <li class="breadcrumb-item active" aria-current="page">Employee Profile</li>
        </ol>
    </nav>

    <!-- Brief Profile Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-light">
            <h1 class="m-0 font-weight-bold text-dark">Application Profile</h1>
            <div>
                <a href="<?= base_url('profile_applications_exercise/profile_position/' . ($position['id'] ?? '#')) ?>" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i> Back to Applications
                </a>
                
                <!-- Profile Status Badge -->
                <?php 
                $statusClass = 'bg-secondary'; 
                $statusText = 'Pending';
                
                if (!empty($application['profile_status'])) {
                    switch($application['profile_status']) {
                        case 'pending':
                            $statusClass = 'bg-warning text-dark';
                            $statusText = 'Pending';
                            break;
                        case 'in_progress':
                            $statusClass = 'bg-info text-dark';
                            $statusText = 'In Progress';
                            break;
                        case 'completed':
                            $statusClass = 'bg-success';
                            $statusText = 'Completed';
                            break;
                    }
                }
                ?>
                <span class="badge <?= $statusClass ?> fs-6 me-2">
                    <i class="fas fa-tasks me-1"></i> Status: <?= $statusText ?>
                </span>
                
                <button class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#profileStatusModal">
                    <i class="fas fa-edit me-1"></i> Update Status
                </button>
                
                <button id="generateProfile" class="btn btn-primary">
                    <i class="fas fa-file-export me-1"></i> Generate Profile
                </button>
            </div>
        </div>
        
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <!-- Profile Status Information -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body p-0">
                            <table class="table mb-0">
                                <tr>
                                    <th class="bg-light" width="25%">Profile Status</th>
                                    <td width="25%"><span class="badge <?= $statusClass ?>"><?= $statusText ?></span></td>
                                    <th class="bg-light" width="25%">Last Updated</th>
                                    <td><?= !empty($application['updated_at']) ? safeFormatDate($application['updated_at'], 'd M Y, h:i A', 'Not updated') : 'Not updated' ?></td>
                                </tr>
                                <?php if ($application['profile_status'] == 'completed'): ?>
                                <tr>
                                    <th class="bg-light">Profiled On</th>
                                    <td><?= !empty($application['profiled_at']) ? safeFormatDate($application['profiled_at'], 'd M Y', 'N/A') : 'N/A' ?></td>
                                    <th class="bg-light">Profiled By</th>
                                    <td><?= !empty($profiledByName) ? esc($profiledByName) : 'User ID: ' . esc($application['profiled_by'] ?? 'N/A') ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Brief Information Section -->
            <div class="row">
                <div class="col-md-8">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <tr>
                                <th class="bg-light" width="20%">Exercise</th>
                                <td><?= esc($exercise['exercise_name'] ?? 'N/A') ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Position Group</th>
                                <td><?= esc($positionGroup['group_name'] ?? 'N/A') ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Position</th>
                                <td><?= esc($position['designation'] ?? 'N/A') ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Application Reference</th>
                                <td><?= esc($application['application_number'] ?? 'N/A') ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Applicant Name</th>
                                <td class="fw-bold"><?= esc($application['fname'] . ' ' . $application['lname']) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Applicant Photo -->
                <div class="col-md-4 text-center">
                    <div class="p-3 border rounded shadow-sm h-100">
                        <div class="mb-2">
                            <h6 class="fw-bold text-dark">Applicant ID Photo</h6>
                        </div>
                        <div class="d-flex align-items-center justify-content-center">
                            <?php if (!empty($application['id_photo_path']) && fileExists($application['id_photo_path'])): ?>
                                <img src="<?= base_url($application['id_photo_path']) ?>" alt="Applicant Photo" class="img-fluid rounded" style="max-height: 200px;">
                            <?php else: ?>
                                <div class="text-center">
                                    <i class="fas fa-user-circle fa-5x text-dark"></i>
                                    <p class="mt-2 text-dark">No photo available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <ul class="nav nav-tabs nav-fill" id="profileTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active text-dark" id="personal-tab" data-bs-toggle="tab" 
                    data-bs-target="#personal" type="button" role="tab" 
                    aria-controls="personal" aria-selected="true">
                <i class="fas fa-user me-2 text-dark"></i><span class="text-dark">Personal Information</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="education-tab" data-bs-toggle="tab" 
                    data-bs-target="#education" type="button" role="tab" 
                    aria-controls="education" aria-selected="false">
                <i class="fas fa-graduation-cap me-2 text-dark"></i><span class="text-dark">Education</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="experience-tab" data-bs-toggle="tab" 
                    data-bs-target="#experience" type="button" role="tab" 
                    aria-controls="experience" aria-selected="false">
                <i class="fas fa-briefcase me-2 text-dark"></i><span class="text-dark">Work Experience</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="files-tab" data-bs-toggle="tab" 
                    data-bs-target="#files" type="button" role="tab" 
                    aria-controls="files" aria-selected="false">
                <i class="fas fa-file-alt me-2 text-dark"></i><span class="text-dark">Uploaded Files</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="profile-details-tab" data-bs-toggle="tab" 
                    data-bs-target="#profile-details" type="button" role="tab" 
                    aria-controls="profile-details" aria-selected="false">
                <i class="fas fa-id-badge me-2 text-dark"></i><span class="text-dark">Employee Profile</span>
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content p-4 border border-top-0 rounded-bottom shadow-sm" id="profileTabsContent">
        <!-- Personal Information Tab -->
        <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
            <div class="row">
                <div class="col-md-12">
                    <h4 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-user-circle text-primary me-2"></i>
                        Personal Information
                    </h4>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="m-0 font-weight-bold">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tr>
                                    <th width="40%">Full Name</th>
                                    <td><?= esc($application['fname'] . ' ' . $application['lname']) ?></td>
                                </tr>
                                <tr>
                                    <th>Gender</th>
                                    <td><?= esc($application['gender'] ?? 'Not specified') ?></td>
                                </tr>
                                <tr>
                                    <th>Date of Birth</th>
                                    <td>
                                        <?= safeFormatDate($application['dobirth']) ?>
                                        <?php if (!empty($application['dobirth'])): ?>
                                            <?php
                                                $dob = new DateTime($application['dobirth']);
                                                $now = new DateTime();
                                                $age = $now->diff($dob)->y;
                                            ?>
                                            <span class="badge bg-secondary ms-2"><?= $age ?> years old</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Place of Origin</th>
                                    <td><?= esc($application['place_of_origin'] ?? 'Not specified') ?></td>
                                </tr>
                                <tr>
                                    <th>Citizenship</th>
                                    <td><?= esc($application['citizenship'] ?? 'Not specified') ?></td>
                                </tr>
                                <tr>
                                    <th>Marital Status</th>
                                    <td>
                                        <?= esc(ucfirst($application['marital_status'] ?? 'Not specified')) ?>
                                        <?php if (!empty($application['date_of_marriage']) && strtolower($application['marital_status']) === 'married'): ?>
                                            <span class="text-muted small ms-2">
                                                (Married on: <?= safeFormatDate($application['date_of_marriage']) ?>)
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if (!empty($application['spouse_employer']) && strtolower($application['marital_status']) === 'married'): ?>
                                <tr>
                                    <th>Spouse's Employer</th>
                                    <td><?= esc($application['spouse_employer']) ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($application['children'])): ?>
                                <tr>
                                    <th>Children</th>
                                    <td>
                                        <?php 
                                        try {
                                            $children = json_decode($application['children'], true);
                                            if (is_array($children) && !empty($children)): 
                                        ?>
                                            <ul class="list-unstyled mb-0">
                                                <?php foreach ($children as $child): ?>
                                                    <li class="mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-child text-primary me-2"></i>
                                                            <div>
                                                                <strong><?= esc($child['name'] ?? 'No Name') ?></strong>
                                                                <?php if (!empty($child['gender'])): ?>
                                                                    <span class="badge <?= strtolower($child['gender']) === 'male' ? 'bg-info' : 'bg-danger' ?> ms-2"><?= esc($child['gender']) ?></span>
                                                                <?php endif; ?>
                                                                <?php if (!empty($child['dob'])): ?>
                                                                    <div>
                                                                        <small class="text-muted">
                                                                            <?php
                                                                            // Validate date before formatting and displaying
                                                                            $date = strtotime($child['dob']);
                                                                            if ($date && $date > 0 && date('Y', $date) > 0) {
                                                                                echo 'Born: ' . safeFormatDate($child['dob']);
                                                                                
                                                                                // Calculate age only if we have a valid date
                                                                                $childDob = new DateTime($child['dob']);
                                                                                $now = new DateTime();
                                                                                $childAge = $now->diff($childDob)->y;
                                                                                echo " ($childAge years old)";
                                                                            } else {
                                                                                echo 'Born: No date provided';
                                                                            }
                                                                            ?>
                                                                        </small>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php 
                                            else: 
                                                echo esc($application['children']); 
                                            endif;
                                        } catch (Exception $e) {
                                            // If JSON parsing fails, display the raw string
                                            echo esc($application['children']);
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="m-0 font-weight-bold">Contact Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tr>
                                    <th width="40%">Contact Details</th>
                                    <td><?= esc($application['contact_details'] ?? 'Not provided') ?></td>
                                </tr>
                                <tr>
                                    <th>Address</th>
                                    <td><?= esc($application['location_address'] ?? 'Not provided') ?></td>
                                </tr>
                                <?php if (!empty($application['id_numbers'])): ?>
                                <tr>
                                    <th>ID Numbers</th>
                                    <td><?= esc($application['id_numbers']) ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                    
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="m-0 font-weight-bold">Current Employment</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tr>
                                    <th width="40%">Current Employer</th>
                                    <td><?= esc($application['current_employer'] ?? 'Not provided') ?></td>
                                </tr>
                                <tr>
                                    <th>Current Position</th>
                                    <td><?= esc($application['current_position'] ?? 'Not provided') ?></td>
                                </tr>
                                <tr>
                                    <th>Current Salary</th>
                                    <td><?= esc($application['current_salary'] ?? 'Not provided') ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="m-0 font-weight-bold">Additional Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-striped">
                                        <tr>
                                            <th width="40%">Offense Convicted</th>
                                            <td>
                                                <?php if (empty($application['offence_convicted'])): ?>
                                                    <span class="text-success">No</span>
                                                <?php else: ?>
                                                    <span class="text-danger">Yes - <?= esc($application['offence_convicted']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>How did you hear about us?</th>
                                            <td><?= esc($application['how_did_you_hear_about_us'] ?? 'Not specified') ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-striped">
                                        <tr>
                                            <th width="40%">Referees</th>
                                            <td>
                                                <?php if (!empty($application['referees'])): ?>
                                                    <?= nl2br(esc($application['referees'])) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">None provided</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Education Tab -->
        <div class="tab-pane fade" id="education" role="tabpanel" aria-labelledby="education-tab">
            <div class="row text-dark">
                <div class="col-md-12 text-dark">
                    <h4 class="border-bottom pb-2 mb-3 text-dark">
                        <i class="fas fa-graduation-cap text-dark me-2"></i>
                        Education History
                    </h4>
                </div>
            </div>
            
            <?php if (empty($education)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No education records found for this applicant.
                </div>
            <?php else: ?>
                <!-- Summary Card -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card border-left-info shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                                                <i class="fas fa-user-graduate text-info fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="text-muted mb-1">Highest Education</h6>
                                                <?php 
                                                $highestEdu = reset($education); // First record (should be highest since they're ordered)
                                                ?>
                                                <h4 class="font-weight-bold mb-0">
                                                    <?= esc($educationModel->getEducationLevelText($highestEdu['education_level'])) ?>
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1 text-muted small">INSTITUTION</p>
                                                <p class="mb-0 font-weight-bold"><?= esc($highestEdu['institution']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1 text-muted small">COURSE/PROGRAM</p>
                                                <p class="mb-0 font-weight-bold"><?= esc($highestEdu['course']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Education Timeline -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="m-0 font-weight-bold">Education Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <?php foreach ($education as $index => $edu): ?>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary">
                                                <?php 
                                                $icon = 'fa-graduation-cap';
                                                switch($edu['education_level']) {
                                                    case $educationModel::LEVEL_PRIMARY:
                                                        $icon = 'fa-school';
                                                        break;
                                                    case $educationModel::LEVEL_SECONDARY:
                                                        $icon = 'fa-school';
                                                        break;
                                                    case $educationModel::LEVEL_CERTIFICATE:
                                                    case $educationModel::LEVEL_DIPLOMA:
                                                        $icon = 'fa-certificate';
                                                        break;
                                                    case $educationModel::LEVEL_BACHELORS:
                                                    case $educationModel::LEVEL_MASTERS:
                                                    case $educationModel::LEVEL_DOCTORATE:
                                                        $icon = 'fa-graduation-cap';
                                                        break;
                                                }
                                                ?>
                                                <i class="fas <?= $icon ?>"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <div class="card mb-3">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <h5 class="mb-0"><?= esc($edu['institution']) ?></h5>
                                                            <span class="badge bg-primary">
                                                                <?= esc($educationModel->getEducationLevelText($edu['education_level'])) ?>
                                                            </span>
                                                        </div>
                                                        <p class="text-primary mb-2"><?= esc($edu['course']) ?></p>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <span class="badge bg-light text-dark">
                                                                    <i class="fas fa-calendar me-1"></i>
                                                                    <?= safeFormatDate($edu['date_from']) ?> - 
                                                                    <?= !empty($edu['date_to']) ? safeFormatDate($edu['date_to']) : 'Present' ?>
                                                                </span>
                                                            </div>
                                                            <?php if (!empty($edu['units'])): ?>
                                                            <div>
                                                                <span class="badge bg-secondary">
                                                                    Units/Grade: <?= esc($edu['units']) ?>
                                                                </span>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Traditional Table Display (Backup) -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="m-0 font-weight-bold">Education Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Level</th>
                                                <th>Institution</th>
                                                <th>Course/Program</th>
                                                <th>Duration</th>
                                                <th>Units/Grade</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($education as $edu): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            <?= esc($educationModel->getEducationLevelText($edu['education_level'])) ?>
                                                        </span>
                                                    </td>
                                                    <td><?= esc($edu['institution']) ?></td>
                                                    <td><?= esc($edu['course']) ?></td>
                                                    <td>
                                                        <?= safeFormatDate($edu['date_from']) ?> - 
                                                        <?= !empty($edu['date_to']) ? safeFormatDate($edu['date_to']) : 'Present' ?>
                                                    </td>
                                                    <td><?= esc($edu['units'] ?? 'N/A') ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Work Experience Tab -->
        <div class="tab-pane fade" id="experience" role="tabpanel" aria-labelledby="experience-tab">
            <div class="row">
                <div class="col-md-12">
                    <h4 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-briefcase text-primary me-2"></i>
                        Work Experience
                    </h4>
                </div>
            </div>
            
            <?php if (empty($experiences)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No work experience records found for this applicant.
                </div>
            <?php else: ?>
                <!-- Summary Card -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card border-left-success shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                                                <i class="fas fa-chart-line text-success fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="text-muted mb-1">Total Experience</h6>
                                                <h4 class="font-weight-bold mb-0">
                                                    <?= isset($experienceSummary->total_years) ? number_format($experienceSummary->total_years, 1) . ' years' : 'Not available' ?>
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <p class="mb-1 text-muted small">POSITIONS HELD</p>
                                                <p class="mb-0 font-weight-bold">
                                                    <?= isset($experienceSummary->total_positions) ? $experienceSummary->total_positions : '0' ?>
                                                </p>
                                            </div>
                                            <div class="col-md-4">
                                                <p class="mb-1 text-muted small">EARLIEST EXPERIENCE</p>
                                                <p class="mb-0 font-weight-bold">
                                                    <?= isset($experienceSummary->earliest_experience) ? safeFormatDate($experienceSummary->earliest_experience) : 'N/A' ?>
                                                </p>
                                            </div>
                                            <div class="col-md-4">
                                                <p class="mb-1 text-muted small">EMPLOYERS</p>
                                                <p class="mb-0 font-weight-bold">
                                                    <?php
                                                    if (isset($experienceSummary->employers)) {
                                                        $employers = explode(',', $experienceSummary->employers);
                                                        echo count($employers);
                                                    } else {
                                                        echo '0';
                                                    }
                                                    ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Work Experience Timeline -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="m-0 font-weight-bold">Experience Timeline</h5>
                            </div>
                            <div class="card-body pb-0">
                                <div class="timeline">
                                    <?php 
                                    $currentYear = null;
                                    foreach ($experiences as $index => $exp): 
                                        $year = date('Y', strtotime($exp['date_from']));
                                        $showYear = ($currentYear !== $year);
                                        $currentYear = $year;
                                    ?>
                                        <?php if ($showYear): ?>
                                        <div class="timeline-year">
                                            <span class="badge bg-primary"><?= $year ?></span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-success">
                                                <i class="fas fa-briefcase"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <div class="card mb-3">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h5 class="mb-0"><?= esc($exp['position']) ?></h5>
                                                            <?php
                                                            // Calculate duration for this position
                                                            $from = new DateTime($exp['date_from']);
                                                            $to = !empty($exp['date_to']) ? new DateTime($exp['date_to']) : new DateTime();
                                                            $interval = $from->diff($to);
                                                            $years = $interval->y + ($interval->m / 12);
                                                            ?>
                                                            <span class="badge bg-success">
                                                                <?= number_format($years, 1) ?> years
                                                            </span>
                                                        </div>
                                                        <div class="d-flex align-items-center mb-2">
                                                            <i class="fas fa-building text-muted me-2"></i>
                                                            <strong class="text-primary"><?= esc($exp['employer']) ?></strong>
                                                        </div>
                                                        
                                                        <div class="small text-muted mb-3">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            <?= safeFormatDate($exp['date_from']) ?> - 
                                                            <?= !empty($exp['date_to']) ? safeFormatDate($exp['date_to']) : 'Present' ?>
                                                            
                                                            <?php if (!empty($exp['employer_contacts_address'])): ?>
                                                                <span class="ms-3">
                                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                                    <?= esc($exp['employer_contacts_address']) ?>
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                        
                                                        <?php if (!empty($exp['work_description'])): ?>
                                                        <div class="mb-2">
                                                            <strong class="d-block mb-1">Job Description:</strong>
                                                            <div class="ps-3 text-muted">
                                                                <?= nl2br(esc($exp['work_description'])) ?>
                                                            </div>
                                                        </div>
                                                        <?php endif; ?>
                                                        
                                                        <?php if (!empty($exp['achievements'])): ?>
                                                        <div>
                                                            <strong class="d-block mb-1">Key Achievements:</strong>
                                                            <div class="ps-3 text-muted">
                                                                <?= nl2br(esc($exp['achievements'])) ?>
                                                            </div>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Documents Tab -->
        <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
            <div class="row">
                <div class="col-md-12">
                    <h4 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        Uploaded Documents
                    </h4>
                </div>
            </div>
            
            <?php if (empty($files)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No files uploaded by this applicant.
                </div>
            <?php else: ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card border-left-dark shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle bg-dark bg-opacity-10 p-3 me-3">
                                                <i class="fas fa-file-archive text-dark fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="text-muted mb-1">Total Files</h6>
                                                <h4 class="font-weight-bold mb-0">
                                                    <?= count($files) ?> document<?= count($files) !== 1 ? 's' : '' ?>
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <p class="text-muted mb-0">The applicant has uploaded the following documents for review. Click on the View or Download buttons to access the files.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="m-0 font-weight-bold">Document Gallery</h5>
                            </div>
                            <div class="card-body pb-0">
                                <div class="row">
                                    <?php foreach ($files as $file): 
                                        $fileExtension = pathinfo($file['file_path'], PATHINFO_EXTENSION);
                                        $iconClass = 'fa-file';
                                        $colorClass = 'text-secondary';
                                        
                                        // Determine file type icon
                                        switch(strtolower($fileExtension)) {
                                            case 'pdf':
                                                $iconClass = 'fa-file-pdf';
                                                $colorClass = 'text-danger';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $iconClass = 'fa-file-word';
                                                $colorClass = 'text-primary';
                                                break;
                                            case 'xls':
                                            case 'xlsx':
                                                $iconClass = 'fa-file-excel';
                                                $colorClass = 'text-success';
                                                break;
                                            case 'ppt':
                                            case 'pptx':
                                                $iconClass = 'fa-file-powerpoint';
                                                $colorClass = 'text-warning';
                                                break;
                                            case 'jpg':
                                            case 'jpeg':
                                            case 'png':
                                            case 'gif':
                                                $iconClass = 'fa-file-image';
                                                $colorClass = 'text-info';
                                                break;
                                            case 'zip':
                                            case 'rar':
                                                $iconClass = 'fa-file-archive';
                                                $colorClass = 'text-dark';
                                                break;
                                        }
                                    ?>
                                        <div class="col-md-4 mb-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <div class="document-icon mb-3">
                                                        <i class="fas <?= $iconClass ?> <?= $colorClass ?> fa-3x"></i>
                                                    </div>
                                                    <h5 class="card-title mb-1"><?= esc($file['file_title']) ?></h5>
                                                    <p class="text-muted small">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        Uploaded: <?= safeFormatDate($file['created_at']) ?>
                                                    </p>
                                                    <?php if (!empty($file['file_description'])): ?>
                                                        <div class="description-box bg-light p-2 rounded mb-3 small">
                                                            <?= nl2br(esc($file['file_description'])) ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="mt-auto">
                                                        <div class="btn-group w-100">
                                                            <?php if (fileExists($file['file_path'])): ?>
                                                                <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-primary" target="_blank">
                                                                    <i class="fas fa-eye me-1"></i> View
                                                                </a>
                                                                <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-secondary" download>
                                                                    <i class="fas fa-download me-1"></i> Download
                                                                </a>
                                                            <?php else: ?>
                                                                <button class="btn btn-sm btn-danger disabled">
                                                                    <i class="fas fa-exclamation-triangle me-1"></i> File Not Found
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Traditional Table Display (Backup) -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="m-0 font-weight-bold">Document List</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="bg-light">
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="25%">File Title</th>
                                                <th width="40%">Description</th>
                                                <th width="15%">Uploaded</th>
                                                <th width="15%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($files as $index => $file): ?>
                                                <tr>
                                                    <td><?= $index + 1 ?></td>
                                                    <td><?= esc($file['file_title']) ?></td>
                                                    <td>
                                                        <?php if (!empty($file['file_description'])): ?>
                                                            <?= nl2br(esc($file['file_description'])) ?>
                                                        <?php else: ?>
                                                            <em class="text-muted">No description</em>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= safeFormatDate($file['created_at']) ?></td>
                                                    <td>
                                                        <?php if (fileExists($file['file_path'])): ?>
                                                            <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-primary" target="_blank">
                                                                <i class="fas fa-eye me-1"></i> View
                                                            </a>
                                                            <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-secondary" download>
                                                                <i class="fas fa-download me-1"></i> Download
                                                            </a>
                                                        <?php else: ?>
                                                            <button class="btn btn-sm btn-danger disabled">
                                                                <i class="fas fa-exclamation-triangle me-1"></i> File Not Found
                                                            </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Profile Details Tab -->
        <div class="tab-pane fade" id="profile-details" role="tabpanel" aria-labelledby="profile-details-tab">
            <div class="mb-4">
                <h4 class="border-bottom pb-2 mb-3 text-dark">
                    <i class="fas fa-id-badge text-dark me-2"></i>
                    Employee Profile Details
                </h4>
                
                <?php if (!empty($profileDetails)): ?>
                    <?php 
                    // Check if this is an AI-generated profile
                    $aiGeneratedProfile = null;
                    if (isset($profileDetails['ai_generated_profile'])) {
                        $aiGeneratedProfile = $profileDetails['ai_generated_profile'];
                    }
                    
                    if ($aiGeneratedProfile): 
                    ?>
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="m-0">
                                        <i class="fas fa-id-badge me-2"></i>
                                        Professional Profile Assessment
                                    </h5>
                                    <button class="btn btn-sm btn-light" onclick="printProfile()">
                                        <i class="fas fa-print me-1"></i> Print Profile
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="ai-profile-content" id="profile-content">
                                    <?= simple_markdown_to_html($aiGeneratedProfile); ?>
                                </div>
                                <div class="text-end mt-3">
                                    <small class="text-muted">
                                        Generated by Gemini AI on <?= !empty($application['profiled_at']) ? safeFormatDate($application['profiled_at']) : safeFormatDate(date('Y-m-d H:i:s')) ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($profileDetails['profile_data'])): ?>
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-light">
                                <h5 class="m-0 text-dark">Profile Data</h5>
                            </div>
                            <div class="card-body">
                                <div class="accordion" id="profileDataAccordion">
                                    <?php foreach ($profileDetails['profile_data'] as $section => $details): ?>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading-<?= esc(str_replace('_', '-', $section)) ?>">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                        data-bs-target="#collapse-<?= esc(str_replace('_', '-', $section)) ?>" 
                                                        aria-expanded="false" aria-controls="collapse-<?= esc(str_replace('_', '-', $section)) ?>">
                                                    <?= esc(ucwords(str_replace('_', ' ', $section))) ?>
                                                </button>
                                            </h2>
                                            <div id="collapse-<?= esc(str_replace('_', '-', $section)) ?>" class="accordion-collapse collapse" 
                                                 aria-labelledby="heading-<?= esc(str_replace('_', '-', $section)) ?>" 
                                                 data-bs-parent="#profileDataAccordion">
                                                <div class="accordion-body">
                                                    <?php if (is_array($details)): ?>
                                                        <?php if (isset($details[0]) && is_array($details[0])): ?>
                                                            <!-- Array of objects/arrays -->
                                                            <div class="table-responsive">
                                                                <table class="table table-bordered table-striped">
                                                                    <thead class="bg-light">
                                                                        <tr>
                                                                            <?php foreach (array_keys($details[0]) as $key): ?>
                                                                                <th><?= esc(ucwords(str_replace('_', ' ', $key))) ?></th>
                                                                            <?php endforeach; ?>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <?php foreach ($details as $item): ?>
                                                                            <tr>
                                                                                <?php foreach ($item as $value): ?>
                                                                                    <td>
                                                                                        <?php if (is_array($value)): ?>
                                                                                            <pre class="mb-0"><?= esc(json_encode($value, JSON_PRETTY_PRINT)) ?></pre>
                                                                                        <?php else: ?>
                                                                                            <?= esc($value) ?>
                                                                                        <?php endif; ?>
                                                                                    </td>
                                                                                <?php endforeach; ?>
                                                                            </tr>
                                                                        <?php endforeach; ?>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        <?php else: ?>
                                                            <!-- Associative array -->
                                                            <div class="table-responsive">
                                                                <table class="table table-bordered">
                                                                    <tbody>
                                                                        <?php foreach ($details as $key => $value): ?>
                                                                            <tr>
                                                                                <th class="bg-light" style="width: 30%;"><?= esc(ucwords(str_replace('_', ' ', $key))) ?></th>
                                                                                <td>
                                                                                    <?php if (is_array($value)): ?>
                                                                                        <pre class="mb-0"><?= esc(json_encode($value, JSON_PRETTY_PRINT)) ?></pre>
                                                                                    <?php else: ?>
                                                                                        <?= esc($value) ?>
                                                                                    <?php endif; ?>
                                                                                </td>
                                                                            </tr>
                                                                        <?php endforeach; ?>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <p><?= esc($details) ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Display profile details in a clean table format instead of small boxes -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-body">
                                <?php foreach ($profileDetails as $section => $details): ?>
                                    <h5 class="border-bottom pb-2 mb-3 text-primary"><?= esc(ucwords(str_replace('_', ' ', $section))) ?></h5>
                                    <?php if (is_array($details)): ?>
                                        <div class="table-responsive mb-4">
                                            <table class="table table-bordered table-striped">
                                                <tbody>
                                                    <?php foreach ($details as $key => $value): ?>
                                                        <tr>
                                                            <th class="bg-light text-dark" style="width: 30%;"><?= esc(ucwords(str_replace('_', ' ', $key))) ?></th>
                                                            <td class="text-dark">
                                                                <?php if (is_array($value)): ?>
                                                                    <pre class="mb-0 text-dark"><?= esc(json_encode($value, JSON_PRETTY_PRINT)) ?></pre>
                                                                <?php else: ?>
                                                                    <?= esc($value) ?>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-dark mb-4"><?= esc($details) ?></p>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No profile details available. Use the "Generate Profile" button to create a profile.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Profile Status Update Modal -->
<div class="modal fade" id="profileStatusModal" tabindex="-1" aria-labelledby="profileStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title text-dark" id="profileStatusModalLabel">Update Profile Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('profile_applications_exercise/profile_update_status/' . $application['id']) ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="profile_status" class="form-label text-dark">Profile Status</label>
                        <select class="form-select" id="profile_status" name="profile_status" required>
                            <option value="pending" <?= ($application['profile_status'] ?? '') == 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="in_progress" <?= ($application['profile_status'] ?? '') == 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="completed" <?= ($application['profile_status'] ?? '') == 'completed' ? 'selected' : '' ?>>Completed</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="profile_details" class="form-label text-dark">Profile Details / Notes</label>
                        <textarea class="form-control" id="profile_details" name="profile_details" rows="5"><?= esc($application['profile_details'] ?? '') ?></textarea>
                        <div class="form-text">Enter any notes or details about the profiling process.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript for Generate Profile Button -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('generateProfile').addEventListener('click', function() {
        // Show loading state
        const btn = this;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Generating...';
        btn.disabled = true;
        
        // Make AJAX request to generate profile
        $.ajax({
            url: '<?= base_url('profile_applications_exercise/generate_profile/' . $application['id']) ?>',
            type: 'POST',
            dataType: 'json',
            // Add CSRF token for security
            headers: {
                'X-CSRF-TOKEN': '<?= csrf_hash() ?>'
            },
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        // Reload the page to show updated profile
                        window.location.reload();
                    });
                } else {
                    // Show error message
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to generate profile',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                // Handle error
                Swal.fire({
                    title: 'Error!',
                    text: 'Something went wrong while generating the profile: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                
                // Reset button
                btn.innerHTML = originalText;
                btn.disabled = false;
            },
            complete: function() {
                // Reset button
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        });
    });
});
</script>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- SweetAlert2 library -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Function to print the profile
function printProfile() {
    const profileContent = document.getElementById('profile-content').innerHTML;
    const applicantName = '<?= esc($application['fname'] . ' ' . $application['lname']) ?>';
    const position = '<?= esc($position['designation'] ?? 'Position') ?>';
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Profile: ${applicantName}</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    line-height: 1.6;
                    padding: 20px;
                }
                h1 { 
                    color: #4e73df; 
                    border-bottom: 2px solid #4e73df; 
                    padding-bottom: 10px;
                }
                .header { 
                    text-align: center;
                    margin-bottom: 30px;
                }
                .profile-content {
                    margin: 20px 0;
                }
                .ai-profile-section {
                    margin-bottom: 20px;
                    padding-bottom: 15px;
                    border-bottom: 1px solid #e3e6f0;
                }
                .ai-profile-section-title {
                    font-weight: bold;
                    font-size: 16px;
                    color: #4e73df;
                    margin-bottom: 10px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #858796;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Professional Profile Assessment</h1>
                <p><strong>Applicant:</strong> ${applicantName} | <strong>Position:</strong> ${position}</p>
            </div>
            <div class="profile-content">
                ${profileContent}
            </div>
            <div class="footer">
                <p>Generated on <?= safeFormatDate(date('Y-m-d H:i:s')); ?> by Gemini AI</p>
            </div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    
    // Add a slight delay to ensure content is fully loaded
    setTimeout(() => {
        printWindow.print();
    }, 500);
}

$(document).ready(function() {
    // Handle profile status form submission
    $('#saveProfileStatus').on('click', function() {
        $('#profileStatusForm').submit();
    });
    
    // Initialize the tabs if needed
    var triggerTabList = [].slice.call(document.querySelectorAll('#profileTabs button'))
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
    
    // Set active tab based on URL hash if present
    let hash = window.location.hash;
    if (hash) {
        $('.nav-tabs button[data-bs-target="' + hash + '"]').tab('show');
    }
    
    // Update URL hash when tab changes
    $('.nav-tabs button').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.getAttribute('data-bs-target');
    });
});
</script>
<?= $this->endSection() ?> 