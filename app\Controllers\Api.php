<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class Api extends ResourceController
{
    protected $format = 'json';

    public function get_provinces()
    {
        try {
            $country_id = $this->request->getPost('country_id');

            // Using dummy data for UI development
            $provinces = [
                ['id' => 1, 'name' => 'National Capital District', 'country_id' => 1],
                ['id' => 2, 'name' => 'Central Province', 'country_id' => 1],
                ['id' => 3, 'name' => 'Western Province', 'country_id' => 1],
                ['id' => 4, 'name' => 'Gulf Province', 'country_id' => 1],
                ['id' => 5, 'name' => 'Milne Bay Province', 'country_id' => 1],
            ];

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $provinces
            ]);
        } catch (\Exception $e) {
            log_message('error', '[API Get Provinces] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching provinces'
            ]);
        }
    }

    public function get_countries()
    {
        try {
            // Using dummy data for UI development
            $countries = [
                ['id' => 1, 'name' => 'Papua New Guinea', 'code' => 'PNG'],
                ['id' => 2, 'name' => 'Australia', 'code' => 'AUS'],
                ['id' => 3, 'name' => 'New Zealand', 'code' => 'NZL'],
            ];

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $countries
            ]);
        } catch (\Exception $e) {
            log_message('error', '[API Get Countries] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching countries'
            ]);
        }
    }

    public function get_districts()
    {
        try {
            $province_id = $this->request->getPost('province_id');

            if (!$province_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Province ID is required'
                ]);
            }

            // Using dummy data for UI development
            $districts = [
                ['id' => 1, 'name' => 'Port Moresby District', 'province_id' => 1],
                ['id' => 2, 'name' => 'Abau District', 'province_id' => 2],
                ['id' => 3, 'name' => 'Kairuku-Hiri District', 'province_id' => 2],
                ['id' => 4, 'name' => 'Rigo District', 'province_id' => 2],
            ];

            return $this->response->setJSON($districts);
        } catch (\Exception $e) {
            log_message('error', '[API Get Districts] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching districts'
            ]);
        }
    }

    public function get_llgs()
    {
        try {
            $district_id = $this->request->getPost('district_id');

            if (!$district_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'District ID is required'
                ]);
            }

            // Using dummy data for UI development
            $llgs = [
                ['id' => 1, 'name' => 'Moresby North-East LLG', 'district_id' => 1],
                ['id' => 2, 'name' => 'Moresby North-West LLG', 'district_id' => 1],
                ['id' => 3, 'name' => 'Moresby South LLG', 'district_id' => 1],
                ['id' => 4, 'name' => 'Abau Rural LLG', 'district_id' => 2],
            ];

            return $this->response->setJSON($llgs);
        } catch (\Exception $e) {
            log_message('error', '[API Get LLGs] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching LLGs'
            ]);
        }
    }
}