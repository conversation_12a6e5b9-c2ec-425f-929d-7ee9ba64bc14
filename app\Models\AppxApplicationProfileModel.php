<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationProfileModel
 *
 * Model for the appx_application_profile table
 */
class AppxApplicationProfileModel extends Model
{
    protected $table         = 'appx_application_profile';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id',
        'name',
        'sex',
        'age',
        'place_origin',
        'contact_details',
        'nid_number',
        'current_employer',
        'current_position',
        'address_location',
        'qualification_text',
        'other_trainings',
        'knowledge',
        'skills_competencies',
        'job_experiences',
        'publications',
        'awards',
        'referees',
        'comments',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'application_id'     => 'required|numeric',
        'name'               => 'required|max_length[255]',
        'sex'                => 'required|max_length[100]',
        'age'                => 'required|numeric|greater_than[0]|less_than[150]',
        'place_origin'       => 'required|max_length[255]',
        'address_location'   => 'required|max_length[255]',
        'qualification_text' => 'required',
        'knowledge'          => 'required',
        'skills_competencies' => 'required',
        'job_experiences'    => 'required',
        'remarks'            => 'required'
    ];

    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'name' => [
            'required'    => 'Name is required',
            'max_length'  => 'Name cannot exceed 255 characters'
        ],
        'sex' => [
            'required'    => 'Sex is required',
            'max_length'  => 'Sex cannot exceed 100 characters'
        ],
        'age' => [
            'required'     => 'Age is required',
            'numeric'      => 'Age must be a number',
            'greater_than' => 'Age must be greater than 0',
            'less_than'    => 'Age must be less than 150'
        ],
        'place_origin' => [
            'required'    => 'Place of origin is required',
            'max_length'  => 'Place of origin cannot exceed 255 characters'
        ],
        'address_location' => [
            'required'    => 'Address location is required',
            'max_length'  => 'Address location cannot exceed 255 characters'
        ],
        'qualification_text' => [
            'required' => 'Qualification text is required'
        ],
        'knowledge' => [
            'required' => 'Knowledge is required'
        ],
        'skills_competencies' => [
            'required' => 'Skills and competencies are required'
        ],
        'job_experiences' => [
            'required' => 'Job experiences are required'
        ],
        'remarks' => [
            'required' => 'Remarks are required'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get profile by application ID
     *
     * @param int $applicationId
     * @return array|null
     */
    public function getProfileByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)->first();
    }

    /**
     * Get profile by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getProfileById($id)
    {
        return $this->find($id);
    }

    /**
     * Search profiles by name
     *
     * @param string $search
     * @return array
     */
    public function searchProfilesByName($search)
    {
        return $this->like('name', $search)->findAll();
    }

    /**
     * Get profiles by age range
     *
     * @param int $minAge
     * @param int $maxAge
     * @return array
     */
    public function getProfilesByAgeRange($minAge, $maxAge)
    {
        return $this->where('age >=', $minAge)
                    ->where('age <=', $maxAge)
                    ->findAll();
    }

    /**
     * Get profiles by sex
     *
     * @param string $sex
     * @return array
     */
    public function getProfilesBySex($sex)
    {
        return $this->where('sex', $sex)->findAll();
    }

    /**
     * Get profiles with publications
     *
     * @return array
     */
    public function getProfilesWithPublications()
    {
        return $this->where('publications IS NOT NULL')
                    ->where('publications !=', '')
                    ->findAll();
    }

    /**
     * Get profiles with awards
     *
     * @return array
     */
    public function getProfilesWithAwards()
    {
        return $this->where('awards IS NOT NULL')
                    ->where('awards !=', '')
                    ->findAll();
    }

    /**
     * Update profile remarks
     *
     * @param int $id
     * @param string $remarks
     * @param int $updatedBy
     * @return bool
     */
    public function updateRemarks($id, $remarks, $updatedBy = null)
    {
        $data = ['remarks' => $remarks];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }
        
        return $this->update($id, $data);
    }

    /**
     * Get profile statistics
     *
     * @return array
     */
    public function getProfileStatistics()
    {
        $stats = [];
        
        // Total profiles
        $stats['total'] = $this->countAllResults(false);
        
        // Profiles by sex
        $sexStats = $this->select('sex, COUNT(*) as count')
                         ->groupBy('sex')
                         ->findAll();
        
        $stats['by_sex'] = [];
        foreach ($sexStats as $stat) {
            $stats['by_sex'][$stat['sex']] = $stat['count'];
        }
        
        // Average age
        $ageStats = $this->select('AVG(age) as avg_age, MIN(age) as min_age, MAX(age) as max_age')
                         ->first();
        
        $stats['age'] = [
            'average' => round($ageStats['avg_age'], 1),
            'minimum' => $ageStats['min_age'],
            'maximum' => $ageStats['max_age']
        ];
        
        return $stats;
    }
}
