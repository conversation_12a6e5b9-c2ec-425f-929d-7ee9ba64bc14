<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section with Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <button type="button" class="btn btn-outline-secondary me-3" onclick="history.back()">
                <i class="fas fa-arrow-left me-2"></i>Back
            </button>
            <div>
                <h1 class="h3 mb-0 text-gray-800">Position Exercises</h1>
                <p class="mb-0 text-muted">Manage position exercises for recruitment</p>
            </div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Position Exercises</li>
            </ol>
        </nav>
    </div>

    <!-- Exercises List Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Draft Exercises</h6>

            <?php
            // Calculate total summary stats
            $totalGroups = 0;
            $totalPositions = 0;
            foreach ($exercises as $ex) {
                $totalGroups += $ex['group_count'];
                $totalPositions += $ex['position_count'];
            }
            ?>
            <div class="d-flex">
                <div class="me-3">
                    <span class="badge bg-info">Total Groups: <?= $totalGroups ?></span>
                </div>
                <div>
                    <span class="badge bg-primary">Total Positions: <?= $totalPositions ?></span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')) : ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="exercisesTable">
                    <thead>
                        <tr>
                            <th>Exercise Name</th>
                            <th>Gazzetted No.</th>
                            <th>Advertisement No.</th>
                            <th>Publish Date From</th>
                            <th>Publish Date To</th>
                            <th>Groups</th>
                            <th>Positions</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($exercises as $exercise) : ?>
                            <tr>
                                <td><?= esc($exercise['exercise_name']) ?></td>
                                <td><?= esc($exercise['gazzetted_no']) ?></td>
                                <td><?= esc($exercise['advertisement_no']) ?></td>
                                <td><?= date('d M Y', strtotime($exercise['publish_date_from'])) ?></td>
                                <td><?= date('d M Y', strtotime($exercise['publish_date_to'])) ?></td>
                                <td>
                                    <span class="badge bg-info"><?= $exercise['group_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?= $exercise['position_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-warning text-dark">Draft</span>
                                </td>
                                <td>
                                    <a href="<?= base_url('positions/positions_groups/' . $exercise['id']) ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <?php if (empty($exercises)) : ?>
                            <tr>
                                <td colspan="9" class="text-center">No draft exercises found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for DataTables -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Wait for jQuery and DataTables to be available
        var checkJquery = setInterval(function() {
            if (window.jQuery && window.jQuery.fn.DataTable) {
                clearInterval(checkJquery);
                jQuery('#exercisesTable').DataTable({
                    "order": [[3, "desc"]], // Sort by publish date from
                    "pageLength": 10,
                    "responsive": true
                });
            }
        }, 100);
    });

    function viewExercise(id) {
        // Redirect to positions groups page
        window.location.href = `<?= base_url('positions/positions_groups') ?>`;
    }
</script>
<?= $this->endSection() ?>