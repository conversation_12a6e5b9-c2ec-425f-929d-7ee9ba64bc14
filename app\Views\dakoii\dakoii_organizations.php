<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Organizations</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="fw-bold text-light-text">Organizations</h3>
            <p class="text-secondary mb-0"><i class="fas fa-info-circle me-2"></i>Manage all organizations in the system</p>
        </div>
        <button type="button" class="btn btn-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#addOrgModal">
            <i class="fas fa-plus me-2"></i> Add Organization
        </button>
    </div>

    <!-- Organization Table Card -->
    <div class="card shadow bg-white border-0 overflow-hidden">
        <div class="card-header bg-primary py-3">
            <div class="d-flex align-items-center">
                <i class="fas fa-building text-white me-2"></i>
                <h5 class="fw-bold mb-0 text-white">Organization List</h5>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-primary">
                        <tr>
                            <th width="50" class="ps-3 text-white">#</th>
                            <th width="70" class="text-white">Logo</th>
                            <th class="text-white">Name</th>
                            <th class="text-white">Code</th>
                            <th class="text-white">Description</th>
                            <th class="text-white">Location Lock</th>
                            <th class="text-white">License</th>
                            <th class="text-white">Status</th>
                            <th width="100" class="text-end pe-3 text-white">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $i = 1; foreach ($organizations as $org): ?>
                        <tr class="bg-light">
                            <td class="ps-3 text-dark"><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($org['orglogo'])): ?>
                                    <img src="<?= imgcheck($org['orglogo']) ?>" alt="Logo" class="rounded-circle shadow-sm" style="height: 48px; width: 48px; object-fit: cover; border: 2px solid var(--primary-color);">
                                <?php else: ?>
                                    <div class="bg-white rounded-circle d-flex align-items-center justify-content-center shadow-sm" style="height: 48px; width: 48px; border: 2px solid var(--primary-color);">
                                        <i class="fas fa-building text-primary" style="font-size: 1.5rem;"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="fw-medium text-dark"><?= esc($org['name']) ?></td>
                            <td><span class="badge bg-primary text-white px-2 py-1"><?= esc($org['orgcode']) ?></span></td>
                            <td class="text-dark">
                                <?php if (!empty($org['description'])): ?>
                                    <span class="d-inline-block text-truncate" style="max-width: 200px; color: #495057;" title="<?= esc($org['description']) ?>">
                                        <?= esc($org['description']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted fst-italic">No description</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($org['is_locationlocked']): ?>
                                    <span class="badge text-dark fw-medium" style="background-color: var(--accent-color);">
                                        <i class="fas fa-map-marker-alt me-1"></i> 
                                        <?= $org['addlockcountry'] ?> - <?= $org['addlockprov'] ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-success text-white">
                                        <i class="fas fa-globe me-1"></i> Unlocked
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge <?= $org['license_status'] ? 'bg-success text-white' : 'bg-danger text-white' ?>">
                                    <i class="fas fa-<?= $org['license_status'] ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                    <?= $org['license_status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge <?= $org['is_active'] ? 'bg-success text-white' : 'bg-danger text-white' ?>">
                                    <i class="fas fa-<?= $org['is_active'] ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                    <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td class="text-end pe-3">
                                <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" 
                                   class="btn btn-sm btn-primary text-white" 
                                   title="View Details">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php if (count($organizations) == 0): ?>
                        <tr class="bg-light">
                            <td colspan="9" class="text-center py-5">
                                <div class="py-5">
                                    <i class="fas fa-building text-muted opacity-25" style="font-size: 3rem;"></i>
                                    <h5 class="text-dark mt-3">No Organizations Found</h5>
                                    <p class="text-muted mb-0">Create your first organization to get started</p>
                                    <button type="button" class="btn btn-sm btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#addOrgModal">
                                        <i class="fas fa-plus me-1"></i> Add Organization
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Organization Modal -->
<div class="modal fade" id="addOrgModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-bg">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white">
                    <i class="fas fa-plus-circle me-2"></i>Add New Organization
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open_multipart('dakoii/organization/create') ?>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col-md-7">
                        <div class="mb-4">
                            <label for="name" class="form-label fw-medium text-light-text">Organization Name</label>
                            <input type="text" class="form-control form-control-lg" id="name" name="name" required 
                                   placeholder="Enter organization name">
                        </div>
                        
                        <div class="mb-4">
                            <label for="description" class="form-label fw-medium text-light-text">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Provide a brief description of the organization"></textarea>
                        </div>
                    </div>
                    
                    <div class="col-md-5">
                        <div class="mb-4">
                            <label for="org_logo" class="form-label fw-medium text-light-text">Organization Logo</label>
                            <div class="text-center mb-3 p-3 bg-lighter-bg rounded">
                                <div id="logo-preview" class="mx-auto mb-3" style="width: 120px; height: 120px; border-radius: 50%; 
                                                           background-color: var(--lighter-bg); 
                                                           display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-building text-primary" style="font-size: 3rem;"></i>
                                </div>
                                <input type="file" class="form-control" id="org_logo" name="org_logo" accept="image/*">
                                <div class="form-text text-secondary mt-2">Recommended size: 200x200 pixels</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary text-white">
                    <i class="fas fa-save me-1"></i> Save Organization
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    // Logo preview functionality
    document.addEventListener('DOMContentLoaded', function() {
        const logoInput = document.getElementById('org_logo');
        const logoPreview = document.getElementById('logo-preview');
        
        logoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    logoPreview.innerHTML = `<img src="${e.target.result}" style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover;">`;
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    });
</script>

<?= $this->endSection() ?> 