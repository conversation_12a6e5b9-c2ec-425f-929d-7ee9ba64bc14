<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);"><?= esc($org['name']) ?></li>
        </ol>
    </nav>

    <!-- Back Button and Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <?php if (!empty($org['orglogo'])): ?>
                        <img src="<?= imgcheck($org['orglogo']) ?>" alt="Logo" class="rounded-circle shadow-sm" 
                             style="height: 64px; width: 64px; object-fit: cover; border: 3px solid var(--primary-color);">
                    <?php else: ?>
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center shadow-sm" 
                             style="height: 64px; width: 64px; border: 3px solid var(--primary-color);">
                            <i class="fas fa-building text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div>
                    <h3 class="mb-1 fw-bold text-light-text"><?= esc($org['name']) ?></h3>
                    <p class="text-secondary mb-0">
                        <i class="fas fa-fingerprint me-2"></i>Organization Code: 
                        <span class="badge bg-lighter-bg text-light-text px-3 py-2 ms-1"><?= esc($org['orgcode']) ?></span>
                    </p>
                </div>
            </div>
        </div>
        <a href="<?= base_url('dakoii/dashboard') ?>" class="btn btn-outline-secondary text-light-text">
            <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
        </a>
    </div>

    <div class="row g-4">
        <!-- Organization Details Card -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-lighter-bg d-flex justify-content-between align-items-center py-3">
                    <h5 class="fw-bold mb-0 text-light-text"><i class="fas fa-building me-2"></i>Organization Details</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary text-white" data-bs-toggle="modal" data-bs-target="#edit">
                            <i class="fas fa-edit me-1"></i> Edit
                        </button>
                        <button type="button" class="btn btn-sm btn-info text-white ms-1" data-bs-toggle="modal" data-bs-target="#license_status">
                            <i class="fas fa-key me-1"></i> License
                        </button>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <!-- Organization Logo -->
                        <div class="col-md-4 text-center mb-4">
                            <div class="p-3 bg-lighter-bg rounded-3">
                                <img class="img-fluid rounded-3 shadow-sm" src="<?= imgcheck($org['orglogo']) ?>" 
                                     alt="Organization Logo" style="max-width: 100%; max-height: 200px; object-fit: contain;">
                                <p class="text-secondary small mt-2 mb-0">Organization Logo</p>
                            </div>
                        </div>
                        <!-- Organization Info -->
                        <div class="col-md-8">
                            <div class="mb-4">
                                <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">About Organization</h6>
                                <p class="text-light mb-0"><?= nl2br(esc($org['description'])) ?></p>
                            </div>
                            
                            <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">Location Settings</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card bg-lighter-bg border-0">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-globe text-primary me-2"></i>
                                                <h6 class="mb-0 text-light">Address Lock Country</h6>
                                            </div>
                                            <p class="mb-0 fw-medium text-light">
                                                <?= isset($country_name) ? esc($country_name) : '<span class="text-muted fst-italic">Not Set</span>' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-lighter-bg border-0">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                                <h6 class="mb-0 text-light">Address Lock Province</h6>
                                            </div>
                                            <p class="mb-0 fw-medium text-light">
                                                <?= isset($province_name) ? esc($province_name) : '<span class="text-muted fst-italic">Not Set</span>' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-<?= $org['is_active'] ? 'success' : 'danger' ?> text-white me-2 px-3 py-2">
                                <i class="fas fa-<?= $org['is_active'] ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                            <span class="badge bg-<?= $org['license_status'] == 'paid' ? 'success' : 'warning' ?> <?= $org['license_status'] == 'paid' ? 'text-white' : 'text-dark' ?> px-3 py-2">
                                <i class="fas fa-<?= $org['license_status'] == 'paid' ? 'check-circle' : 'exclamation-circle' ?> me-1"></i>
                                License: <?= ucfirst($org['license_status']) ?>
                            </span>
                        </div>
                        <small class="text-dark">
                            <i class="fas fa-database me-1"></i> Organization ID: <?= $org['id'] ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Administrators Card -->
        <div class="col-lg-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-lighter-bg d-flex justify-content-between align-items-center py-3">
                    <h5 class="fw-bold mb-0 text-light-text"><i class="fas fa-user-shield me-2"></i>System Administrators</h5>
                    <button type="button" class="btn btn-sm btn-success text-white" data-bs-toggle="modal" data-bs-target="#sysadmin">
                        <i class="fas fa-plus me-1"></i> New Admin
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-lighter-bg">
                                <tr>
                                    <th class="ps-3 text-light-text">Name</th>
                                    <th class="text-light-text">Role</th>
                                    <th class="text-light-text">Status</th>
                                    <th class="text-end pe-3 text-light-text"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($admins)): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <div class="py-4">
                                            <i class="fas fa-user-shield text-muted opacity-25" style="font-size: 3rem;"></i>
                                            <h6 class="text-light-text mt-3">No Administrators Found</h6>
                                            <p class="text-muted mb-3 small">Create an administrator for this organization</p>
                                            <button type="button" class="btn btn-sm btn-success text-white" data-bs-toggle="modal" data-bs-target="#sysadmin">
                                                <i class="fas fa-plus me-1"></i> Add Administrator
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($admins as $ur): ?>
                                <tr>
                                    <td class="ps-3">
                                        <div>
                                            <div class="fw-medium text-light-text"><?= esc($ur['name']) ?></div>
                                            <small class="text-muted"><i class="fas fa-user me-1"></i><?= esc($ur['username']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-white">
                                            <i class="fas fa-user-tag me-1"></i> <?= ucfirst(esc($ur['role'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= isset($ur['status']) && $ur['status'] == 1 ? 'success' : 'danger' ?> text-white">
                                            <i class="fas fa-<?= isset($ur['status']) && $ur['status'] == 1 ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                            <?= isset($ur['status']) && $ur['status'] == 1 ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td class="text-end pe-3">
                                        <button class="btn btn-sm btn-primary text-white edit-admin" 
                                                data-id="<?= $ur['id'] ?>"
                                                data-name="<?= esc($ur['name']) ?>"
                                                data-username="<?= esc($ur['username']) ?>"
                                                data-role="<?= esc($ur['role']) ?>"
                                                data-active="<?= isset($ur['status']) ? $ur['status'] : '0' ?>"
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editAdminModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organization Exercises -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="fw-bold mb-0 text-white"><i class="fas fa-clipboard-list me-2"></i>Organization Exercises</h5>
                        <?php if (isset($can_add_exercise) && $can_add_exercise): ?>
                        <a href="<?= base_url('exercises/create?org_id=' . $org['id']) ?>" class="btn btn-sm btn-light">
                            <i class="fas fa-plus me-1"></i> Add Exercise
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="ps-3 text-dark">#</th>
                                    <th class="text-dark">Exercise Name</th>
                                    <th class="text-dark">Advertisement</th>
                                    <th class="text-dark">Date Range</th>
                                    <th class="text-dark">Status</th>
                                    <th class="text-dark text-end pe-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($exercises) && !empty($exercises)): ?>
                                    <?php $i = 1; foreach ($exercises as $exercise): ?>
                                    <tr class="bg-light">
                                        <td class="ps-3 text-dark"><?= $i++ ?></td>
                                        <td class="fw-medium text-dark"><?= esc($exercise['exercise_name']) ?></td>
                                        <td class="text-dark">
                                            <?php if (!empty($exercise['advertisement_no'])): ?>
                                                <span class="badge bg-primary text-white px-2 py-1">
                                                    <?= esc($exercise['advertisement_no']) ?>
                                                </span>
                                                <?php if (!empty($exercise['advertisement_date'])): ?>
                                                    <small class="text-muted d-block mt-1">
                                                        <?= date('d M Y', strtotime($exercise['advertisement_date'])) ?>
                                                    </small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted fst-italic">Not set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-dark">
                                            <?php if(!empty($exercise['publish_date_from']) && !empty($exercise['publish_date_to'])): ?>
                                                <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> - 
                                                <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted fst-italic">Not set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = 'secondary';
                                            $statusIcon = 'circle';
                                            switch($exercise['status']) {
                                                case 'draft':
                                                    $statusClass = 'secondary';
                                                    $statusIcon = 'pencil-alt';
                                                    break;
                                                case 'publish':
                                                    $statusClass = 'success';
                                                    $statusIcon = 'check-circle';
                                                    break;
                                                case 'publish_request':
                                                    $statusClass = 'warning';
                                                    $statusIcon = 'clock';
                                                    break;
                                                case 'selection':
                                                    $statusClass = 'info';
                                                    $statusIcon = 'tasks';
                                                    break;
                                                case 'review':
                                                    $statusClass = 'primary';
                                                    $statusIcon = 'search';
                                                    break;
                                                case 'closed':
                                                    $statusClass = 'danger';
                                                    $statusIcon = 'times-circle';
                                                    break;
                                            }
                                            $textColor = ($statusClass == 'warning' || $statusClass == 'light') ? 'text-dark' : 'text-white';
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?> <?= $textColor ?>">
                                                <i class="fas fa-<?= $statusIcon ?> me-1"></i> 
                                                <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                                            </span>
                                        </td>
                                        <td class="text-end pe-3">
                                            <div class="btn-group">
                                                <a href="<?= base_url('exercises/get/' . $exercise['id']) ?>" class="btn btn-sm btn-info text-white">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-primary text-white change-status-btn" 
                                                        data-id="<?= $exercise['id'] ?>"
                                                        data-name="<?= esc($exercise['exercise_name']) ?>"
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#changeStatusModal<?= $exercise['id'] ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                <tr class="bg-light">
                                    <td colspan="6" class="text-center py-5">
                                        <div class="py-4">
                                            <i class="fas fa-clipboard-list text-muted opacity-25" style="font-size: 3rem;"></i>
                                            <h6 class="text-dark mt-3">No Exercises Found</h6>
                                            <p class="text-muted mb-0">There are no exercises for this organization yet.</p>
                                            <?php if (isset($can_add_exercise) && $can_add_exercise): ?>
                                            <a href="<?= base_url('exercises/create?org_id=' . $org['id']) ?>" class="btn btn-sm btn-primary mt-3">
                                                <i class="fas fa-plus me-1"></i> Add Exercise
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Organization Modal -->
<div class="modal fade" id="edit" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-bg">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white">
                    <i class="fas fa-edit me-2"></i> Edit Organization
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open_multipart('dakoii/organization/update') ?>
            <div class="modal-body p-4">
                <div class="row g-4">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">Basic Information</h6>
                        
                        <div class="mb-3">
                            <label for="orgcode" class="form-label text-light-text">Organization Code</label>
                            <input type="text" class="form-control bg-lighter-bg" name="orgcode" id="orgcode" value="<?= esc($org['orgcode']) ?>" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label text-light-text">Organization Name</label>
                            <input type="text" class="form-control" name="name" id="name" value="<?= esc($org['name']) ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label text-light-text">Description</label>
                            <textarea class="form-control" name="description" id="description" rows="4"><?= esc($org['description']) ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label text-light-text">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="1" <?= $org['is_active'] == 1 ? 'selected' : '' ?>>Active</option>
                                <option value="0" <?= $org['is_active'] == 0 ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                    </div>

                    <!-- Location and Logo -->
                    <div class="col-md-6">
                        <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">Location Settings</h6>

                        <div class="mb-3">
                            <label for="country" class="form-label text-light-text">Address Lock Country</label>
                            <select name="country" id="country" class="form-select">
                                <option value="">Select Country</option>
                                <option value="<?= $set_country['id'] ?>" <?= $org['addlockcountry'] == $set_country['id'] ? 'selected' : '' ?>>
                                    <?= esc($set_country['name']) ?>
                                </option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="province" class="form-label text-light-text">Address Lock Province</label>
                            <select name="province" id="province" class="form-select">
                                <option value="">Select Province</option>
                                <?php foreach ($get_provinces as $prov): ?>
                                    <option value="<?= $prov['id'] ?>" 
                                            <?= $org['addlockprov'] == $prov['id'] ? 'selected' : '' ?>>
                                        <?= esc($prov['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <h6 class="text-primary fw-bold mb-3 border-bottom pb-2 mt-4">Organization Logo</h6>

                        <div class="mb-3">
                            <label for="org_logo" class="form-label text-light-text">Upload New Logo</label>
                            <input type="file" class="form-control" name="org_logo" id="org_logo" accept="image/*">
                            <div class="form-text text-secondary">Current logo will be replaced if new one is uploaded</div>
                        </div>

                        <?php if ($org['orglogo']): ?>
                        <div class="mt-2">
                            <div class="text-center p-3 bg-lighter-bg rounded">
                                <img src="<?= imgcheck($org['orglogo']) ?>" alt="Current Logo" class="img-thumbnail" style="height: 100px; object-fit: contain;">
                                <div class="mt-2 text-secondary small">Current Logo</div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" name="id" value="<?= $org['id'] ?>">
                <button type="button" class="btn btn-secondary text-white" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary text-white">
                    <i class="fas fa-save me-1"></i> Save Changes
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Admin Modal -->
<div class="modal fade" id="editAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-bg">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white">
                    <i class="fas fa-user-edit me-2"></i> Edit System Admin
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('dakoii/organization/admin/update') ?>
            <div class="modal-body p-4">
                <input type="hidden" name="id" id="edit_admin_id">
                <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
                
                <div class="mb-3">
                    <label for="edit_admin_name" class="form-label text-light-text">Name</label>
                    <input type="text" class="form-control" name="name" id="edit_admin_name" required>
                </div>
                
                <div class="mb-3">
                    <label for="edit_admin_username" class="form-label text-light-text">Username</label>
                    <input type="text" class="form-control" name="username" id="edit_admin_username" required>
                </div>
                
                <div class="mb-3">
                    <label for="edit_admin_password" class="form-label text-light-text">Password (leave blank to keep current)</label>
                    <input type="password" class="form-control" name="password" id="edit_admin_password">
                    <div class="form-text text-secondary">Only fill this if you want to change the password</div>
                </div>
                
                <div class="mb-3">
                    <label for="edit_admin_role" class="form-label text-light-text">Role</label>
                    <select class="form-select" name="role" id="edit_admin_role" required>
                        <option value="admin">Administrator</option>
                        <option value="user">Standard User</option>
                    </select>
                </div>
                
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="edit_admin_active" name="is_active" value="1">
                    <label class="form-check-label text-light-text" for="edit_admin_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary text-white">
                    <i class="fas fa-save me-1"></i> Update Admin
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- License Status Modal -->
<div class="modal fade" id="license_status" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-bg">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i> Update License Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('dakoii/organization/update-license') ?>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <div class="d-inline-block p-3 rounded-circle bg-warning bg-opacity-25 mb-3">
                        <i class="fas fa-key text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="text-light-text">Update Organization License</h5>
                    <p class="text-secondary">Change the license status for this organization</p>
                </div>
                
                <div class="mb-3">
                    <label for="license_status_select" class="form-label text-light-text">License Status</label>
                    <select class="form-select form-select-lg" name="license_status" id="license_status_select" required>
                        <option value="paid" <?= $org['license_status'] == 'paid' ? 'selected' : '' ?>>Paid</option>
                        <option value="unpaid" <?= $org['license_status'] == 'unpaid' ? 'selected' : '' ?>>Unpaid</option>
                        <option value="trial" <?= $org['license_status'] == 'trial' ? 'selected' : '' ?>>Trial</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" name="id" value="<?= $org['id'] ?>">
                <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
                <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-warning text-dark fw-bold">
                    <i class="fas fa-save me-1"></i> Update License
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Add Admin Modal -->
<div class="modal fade" id="sysadmin" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-bg">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white">
                    <i class="fas fa-user-plus me-2"></i> Add New Admin
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('dakoii/organization/admin/create') ?>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <div class="d-inline-block p-3 rounded-circle bg-success bg-opacity-25 mb-3">
                        <i class="fas fa-user-shield text-success" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="text-light-text">Create System Administrator</h5>
                    <p class="text-secondary">Add a new administrator account for this organization</p>
                </div>
                
                <div class="mb-3">
                    <label for="admin_name" class="form-label text-light-text">Full Name</label>
                    <input type="text" class="form-control" name="name" id="admin_name" required 
                           placeholder="Enter administrator's full name">
                </div>
                
                <div class="mb-3">
                    <label for="admin_username" class="form-label text-light-text">Username</label>
                    <input type="text" class="form-control" name="username" id="admin_username" required 
                           placeholder="Enter unique username">
                </div>
                
                <div class="mb-3">
                    <label for="admin_password" class="form-label text-light-text">Password</label>
                    <input type="password" class="form-control" name="password" id="admin_password" required 
                           placeholder="Enter secure password">
                </div>
                
                <div class="mb-3">
                    <label for="admin_role" class="form-label text-light-text">Role</label>
                    <select class="form-select" name="role" id="admin_role" required>
                        <option value="admin">Administrator</option>
                        <option value="user">Standard User</option>
                    </select>
                </div>
                
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="admin_active" name="is_active" value="1" checked>
                    <label class="form-check-label text-light-text" for="admin_active">Active Account</label>
                </div>
                
                <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-success text-white">
                    <i class="fas fa-save me-1"></i> Create Admin
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Exercise Status Change Modals -->
<?php if (isset($exercises) && !empty($exercises)): ?>
    <?php foreach ($exercises as $exercise): ?>
    <div class="modal fade" id="changeStatusModal<?= $exercise['id'] ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content bg-dark-bg">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-edit me-2"></i> Change Exercise Status
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open('dakoii/exercise/change-status/' . $exercise['id'] . '?redirect=org/' . $org['orgcode']) ?>
                <div class="modal-body p-4">
                    <!-- Add CSRF token -->
                    <?= csrf_field() ?>
                    
                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-primary bg-opacity-25 mb-3">
                            <i class="fas fa-clipboard-list text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="text-light-text"><?= esc($exercise['exercise_name']) ?></h5>
                        <p class="text-secondary">Current Status: 
                            <?php
                            $statusClass = 'secondary';
                            switch($exercise['status']) {
                                case 'draft': $statusClass = 'secondary'; break;
                                case 'publish': $statusClass = 'success'; break;
                                case 'publish_request': $statusClass = 'warning'; break;
                                case 'selection': $statusClass = 'info'; break;
                                case 'review': $statusClass = 'primary'; break;
                                case 'closed': $statusClass = 'danger'; break;
                            }
                            $textColor = ($statusClass == 'warning' || $statusClass == 'light') ? 'text-dark' : 'text-white';
                            ?>
                            <span class="badge bg-<?= $statusClass ?> <?= $textColor ?>">
                                <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status_<?= $exercise['id'] ?>" class="form-label text-light-text">New Status</label>
                        <select class="form-select form-select-lg" name="status" id="status_<?= $exercise['id'] ?>" required>
                            <option value="draft" <?= $exercise['status'] == 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="publish" <?= $exercise['status'] == 'publish' ? 'selected' : '' ?>>Publish</option>
                            <option value="publish_request" <?= $exercise['status'] == 'publish_request' ? 'selected' : '' ?>>Publish Request</option>
                            <option value="selection" <?= $exercise['status'] == 'selection' ? 'selected' : '' ?>>Selection</option>
                            <option value="review" <?= $exercise['status'] == 'review' ? 'selected' : '' ?>>Review</option>
                            <option value="closed" <?= $exercise['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                        </select>
                        <div class="form-text text-secondary mt-2">
                            <ul class="ps-3 mb-0">
                                <li><b>Draft</b>: Work in progress, not visible to public</li>
                                <li><b>Publish Request</b>: Pending approval for publication</li>
                                <li><b>Publish</b>: Publicly visible exercise</li>
                                <li><b>Selection</b>: In selection phase</li>
                                <li><b>Review</b>: Under review</li>
                                <li><b>Closed</b>: Exercise is closed and archived</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary text-white">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

<script>
    // Initialize admin edit modal
    document.addEventListener('DOMContentLoaded', function() {
        var editAdminModal = document.getElementById('editAdminModal');
        if (editAdminModal) {
            document.querySelectorAll('.edit-admin').forEach(function(button) {
                button.addEventListener('click', function() {
                    document.getElementById('edit_admin_id').value = this.getAttribute('data-id');
                    document.getElementById('edit_admin_name').value = this.getAttribute('data-name');
                    document.getElementById('edit_admin_username').value = this.getAttribute('data-username');
                    document.getElementById('edit_admin_role').value = this.getAttribute('data-role');
                    document.getElementById('edit_admin_active').checked = this.getAttribute('data-active') === '1';
                });
            });
        }
    });
</script>

<?= $this->endSection() ?>