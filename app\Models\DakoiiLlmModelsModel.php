<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * DakoiiLlmModelsModel
 *
 * Model for the dakoii_llm_models table
 */
class DakoiiLlmModelsModel extends Model
{
    protected $table         = 'dakoii_llm_models';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'model_name',
        'provider',
        'api_key',
        'base_url',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'model_name' => 'required|max_length[100]',
        'provider'   => 'required|max_length[50]',
        'api_key'    => 'required|max_length[255]',
        'base_url'   => 'permit_empty|max_length[255]|valid_url',
        'is_active'  => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'model_name' => [
            'required'    => 'Model name is required',
            'max_length'  => 'Model name cannot exceed 100 characters'
        ],
        'provider' => [
            'required'    => 'Provider is required',
            'max_length'  => 'Provider cannot exceed 50 characters'
        ],
        'api_key' => [
            'required'    => 'API key is required',
            'max_length'  => 'API key cannot exceed 255 characters'
        ],
        'base_url' => [
            'max_length'  => 'Base URL cannot exceed 255 characters',
            'valid_url'   => 'Base URL must be a valid URL'
        ],
        'is_active' => [
            'in_list' => 'Is active must be 0 or 1'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get active LLM models
     *
     * @return array
     */
    public function getActiveModels()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get model by name
     *
     * @param string $modelName
     * @return array|null
     */
    public function getModelByName($modelName)
    {
        return $this->where('model_name', $modelName)->first();
    }

    /**
     * Get models by provider
     *
     * @param string $provider
     * @return array
     */
    public function getModelsByProvider($provider)
    {
        return $this->where('provider', $provider)->findAll();
    }

    /**
     * Get model by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getModelById($id)
    {
        return $this->find($id);
    }

    /**
     * Activate model
     *
     * @param int $id
     * @return bool
     */
    public function activateModel($id)
    {
        return $this->update($id, ['is_active' => 1]);
    }

    /**
     * Deactivate model
     *
     * @param int $id
     * @return bool
     */
    public function deactivateModel($id)
    {
        return $this->update($id, ['is_active' => 0]);
    }

    /**
     * Deactivate all models
     *
     * @return bool
     */
    public function deactivateAllModels()
    {
        return $this->set('is_active', 0)->update();
    }

    /**
     * Set as primary model (deactivate others and activate this one)
     *
     * @param int $id
     * @return bool
     */
    public function setPrimaryModel($id)
    {
        // Start transaction
        $this->db->transStart();
        
        // Deactivate all models
        $this->deactivateAllModels();
        
        // Activate the selected model
        $this->activateModel($id);
        
        // Complete transaction
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * Get primary (active) model
     *
     * @return array|null
     */
    public function getPrimaryModel()
    {
        return $this->where('is_active', 1)->first();
    }

    /**
     * Search models by name or provider
     *
     * @param string $search
     * @return array
     */
    public function searchModels($search)
    {
        return $this->groupStart()
                    ->like('model_name', $search)
                    ->orLike('provider', $search)
                    ->groupEnd()
                    ->findAll();
    }

    /**
     * Get model statistics
     *
     * @return array
     */
    public function getModelStatistics()
    {
        $stats = [];
        
        // Total models
        $stats['total'] = $this->countAllResults(false);
        
        // Active models
        $stats['active'] = $this->where('is_active', 1)->countAllResults(false);
        
        // Inactive models
        $stats['inactive'] = $this->where('is_active', 0)->countAllResults(false);
        
        // Models by provider
        $providerStats = $this->select('provider, COUNT(*) as count')
                              ->groupBy('provider')
                              ->findAll();
        
        $stats['by_provider'] = [];
        foreach ($providerStats as $stat) {
            $stats['by_provider'][$stat['provider']] = $stat['count'];
        }
        
        return $stats;
    }

    /**
     * Update API key for model
     *
     * @param int $id
     * @param string $apiKey
     * @return bool
     */
    public function updateApiKey($id, $apiKey)
    {
        return $this->update($id, ['api_key' => $apiKey]);
    }

    /**
     * Update base URL for model
     *
     * @param int $id
     * @param string $baseUrl
     * @return bool
     */
    public function updateBaseUrl($id, $baseUrl)
    {
        return $this->update($id, ['base_url' => $baseUrl]);
    }

    /**
     * Test model connection (placeholder method)
     *
     * @param int $id
     * @return bool
     */
    public function testModelConnection($id)
    {
        $model = $this->find($id);
        
        if (!$model) {
            return false;
        }
        
        // TODO: Implement actual connection test based on provider
        // This is a placeholder that always returns true
        // In a real implementation, you would test the API connection
        
        return true;
    }
}
