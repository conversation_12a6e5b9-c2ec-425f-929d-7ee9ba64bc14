<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;

class IncomingApplicantsController extends BaseController
{
    use ResponseTrait;

    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
    }

    /**
     * Display the list of unacknowledged applications
     */
    public function index()
    {
        // Mock positions data based on PositionsModel structure
        $positions = [
            1 => [
                'id' => 1,
                'position_reference' => 'IT-SR-001',
                'designation' => 'Senior Software Developer',
                'department' => 'Information Technology',
                'org_name' => 'Department of ICT'
            ],
            2 => [
                'id' => 2,
                'position_reference' => 'HR-MGR-001',
                'designation' => 'HR Manager',
                'department' => 'Human Resources',
                'org_name' => 'Department of Personnel Management'
            ],
            3 => [
                'id' => 3,
                'position_reference' => 'FIN-ACC-001',
                'designation' => 'Senior Accountant',
                'department' => 'Finance',
                'org_name' => 'Department of Finance'
            ],
            4 => [
                'id' => 4,
                'position_reference' => 'EDU-TCH-001',
                'designation' => 'Mathematics Teacher',
                'department' => 'Education',
                'org_name' => 'Department of Education'
            ],
            5 => [
                'id' => 5,
                'position_reference' => 'HLT-NUR-001',
                'designation' => 'Registered Nurse',
                'department' => 'Health Services',
                'org_name' => 'Department of Health'
            ]
        ];

        // Dummy data for UI development with position details
        $applications = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'applicant_id' => 101,
                'fname' => 'John',
                'lname' => 'Doe',
                'email' => '<EMAIL>',
                'contact_details' => '+************',
                'position_id' => 1,
                'position_reference' => $positions[1]['position_reference'],
                'position_title' => $positions[1]['designation'],
                'department' => $positions[1]['department'],
                'org_name' => $positions[1]['org_name'],
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-10 09:00:00',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'applicant_id' => 102,
                'fname' => 'Jane',
                'lname' => 'Smith',
                'email' => '<EMAIL>',
                'contact_details' => '+675 987 6543',
                'position_id' => 2,
                'position_reference' => $positions[2]['position_reference'],
                'position_title' => $positions[2]['designation'],
                'department' => $positions[2]['department'],
                'org_name' => $positions[2]['org_name'],
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-09 11:15:00',
                'status' => 'pending'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'applicant_id' => 103,
                'fname' => 'Michael',
                'lname' => 'Johnson',
                'email' => '<EMAIL>',
                'contact_details' => '+675 456 7890',
                'position_id' => 3,
                'position_reference' => $positions[3]['position_reference'],
                'position_title' => $positions[3]['designation'],
                'department' => $positions[3]['department'],
                'org_name' => $positions[3]['org_name'],
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-08 13:30:00',
                'status' => 'pending'
            ],
            [
                'id' => 4,
                'application_number' => 'APP-2024-004',
                'applicant_id' => 104,
                'fname' => 'Sarah',
                'lname' => 'Williams',
                'email' => '<EMAIL>',
                'contact_details' => '+675 321 0987',
                'position_id' => 4,
                'position_reference' => $positions[4]['position_reference'],
                'position_title' => $positions[4]['designation'],
                'department' => $positions[4]['department'],
                'org_name' => $positions[4]['org_name'],
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-07 15:45:00',
                'status' => 'pending'
            ],
            [
                'id' => 5,
                'application_number' => 'APP-2024-005',
                'applicant_id' => 105,
                'fname' => 'David',
                'lname' => 'Brown',
                'email' => '<EMAIL>',
                'contact_details' => '+675 654 3210',
                'position_id' => 5,
                'position_reference' => $positions[5]['position_reference'],
                'position_title' => $positions[5]['designation'],
                'department' => $positions[5]['department'],
                'org_name' => $positions[5]['org_name'],
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-06 08:20:00',
                'status' => 'pending'
            ]
        ];

        $data = [
            'title' => 'Incoming Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('incoming_applicants/incoming_applicants_index', $data);
    }

    /**
     * Mark an application as received/acknowledged
     */
    public function acknowledge($id)
    {
        $data = [
            'recieved_acknowledged' => date('Y-m-d H:i:s'),
            'recieved_by' => $this->session->get('user_id') ?? 1,
            'recieved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id') ?? 1,
            'status' => 'active'
        ];

        try {
            // Simulate successful update for UI development
            $updateSuccess = true; // Replace with actual model update call later

            if ($updateSuccess) {
                // Dummy application data for email notification
                $application = [
                    'id' => $id,
                    'application_number' => 'APP-2024-' . str_pad($id, 3, '0', STR_PAD_LEFT),
                    'applicant_id' => 101,
                    'fname' => 'John',
                    'lname' => 'Doe',
                    'email' => '<EMAIL>'
                ];

                // Simulate email sending for UI development
                $emailSent = true; // Replace with actual email sending later
                log_message('debug', 'Acknowledgment email result: ' . ($emailSent ? 'success' : 'failed'));

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application successfully acknowledged',
                    'email_sent' => $emailSent,
                    'csrf_hash' => csrf_hash()
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to acknowledge application',
                    'csrf_hash' => csrf_hash()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error acknowledging application: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    /**
     * Send acknowledgment email to applicant
     */
    private function sendAcknowledgmentEmail($application)
    {
        try {
            // Simulate email sending for UI development - replace with actual email logic later
            log_message('info', 'Simulated acknowledgment email sent to: ' . $application['email']);
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Exception while sending acknowledgment email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * View application details
     */
    public function view($id)
    {
        // Mock comprehensive application data for UI development
        $applications = [
            1 => [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'applicant_id' => 101,
                'fname' => 'John',
                'lname' => 'Doe',
                'email' => '<EMAIL>',
                'contact_details' => '+************',
                'gender' => 'Male',
                'dobirth' => '1990-05-15',
                'place_of_origin' => 'Port Moresby, NCD',
                'location_address' => '123 Main Street, Port Moresby, NCD',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Single',
                'current_employer' => 'Tech Solutions PNG',
                'current_position' => 'Junior Developer',
                'position_id' => 1,
                'position_reference' => 'IT-SR-001',
                'position_title' => 'Senior Software Developer',
                'department' => 'Information Technology',
                'org_name' => 'Department of ICT',
                'qualifications' => 'Bachelor of Computer Science - University of Papua New Guinea (2018)',
                'experience_summary' => '3 years experience in web development using PHP, JavaScript, and MySQL',
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-10 09:00:00',
                'status' => 'pending',
                'id_photo_path' => '',
                'signature_path' => '',
                'cv_path' => 'uploads/applications/cv_john_doe.pdf',
                'cover_letter_path' => 'uploads/applications/cover_letter_john_doe.pdf'
            ],
            2 => [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'applicant_id' => 102,
                'fname' => 'Jane',
                'lname' => 'Smith',
                'email' => '<EMAIL>',
                'contact_details' => '+675 987 6543',
                'gender' => 'Female',
                'dobirth' => '1988-08-22',
                'place_of_origin' => 'Lae, Morobe',
                'location_address' => '456 Oak Avenue, Lae, Morobe',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Married',
                'current_employer' => 'Business Corp PNG',
                'current_position' => 'HR Assistant',
                'position_id' => 2,
                'position_reference' => 'HR-MGR-001',
                'position_title' => 'HR Manager',
                'department' => 'Human Resources',
                'org_name' => 'Department of Personnel Management',
                'qualifications' => 'Master of Human Resource Management - University of Papua New Guinea (2015)',
                'experience_summary' => '8 years experience in human resources, recruitment, and staff development',
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-09 11:15:00',
                'status' => 'pending',
                'id_photo_path' => '',
                'signature_path' => '',
                'cv_path' => 'uploads/applications/cv_jane_smith.pdf',
                'cover_letter_path' => 'uploads/applications/cover_letter_jane_smith.pdf'
            ]
        ];

        // Get application data or use default for demo
        $application = $applications[$id] ?? $applications[1];
        $application['id'] = $id; // Ensure ID matches the requested one

        if (!$application) {
            return redirect()->to('incoming_applications')->with('error', 'Application not found');
        }

        $data = [
            'title' => 'Application Details - ' . $application['application_number'],
            'menu' => 'applications',
            'application' => $application
        ];

        return view('incoming_applicants/incoming_applicants_view', $data);
    }

    /**
     * Batch acknowledge multiple applications
     */
    public function batchAcknowledge()
    {
        $ids = $this->request->getPost('ids');

        if (empty($ids)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No applications selected',
                'csrf_hash' => csrf_hash()
            ]);
        }

        $data = [
            'recieved_acknowledged' => date('Y-m-d H:i:s'),
            'recieved_by' => $this->session->get('user_id') ?? 1,
            'recieved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id') ?? 1,
            'status' => 'active'
        ];

        $successCount = 0;
        $failCount = 0;
        $emailSentCount = 0;

        foreach ($ids as $id) {
            try {
                // Simulate successful update for UI development
                $updateSuccess = true; // Replace with actual model update call later

                if ($updateSuccess) {
                    $successCount++;

                    // Dummy application data for email notification
                    $application = [
                        'id' => $id,
                        'application_number' => 'APP-2024-' . str_pad($id, 3, '0', STR_PAD_LEFT),
                        'applicant_id' => 101,
                        'fname' => 'John',
                        'lname' => 'Doe',
                        'email' => '<EMAIL>'
                    ];

                    // Simulate email sending
                    if ($application) {
                        $emailSent = $this->sendAcknowledgmentEmail($application);
                        if ($emailSent) $emailSentCount++;
                    }
                } else {
                    $failCount++;
                }
            } catch (\Exception $e) {
                log_message('error', 'Error in batch acknowledge for ID ' . $id . ': ' . $e->getMessage());
                $failCount++;
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => "$successCount applications acknowledged successfully. $failCount failed. $emailSentCount email notifications sent.",
            'csrf_hash' => csrf_hash()
        ]);
    }
}