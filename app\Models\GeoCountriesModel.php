<?php namespace App\Models;

use CodeIgniter\Model;

/**
 * GeoCountriesModel
 *
 * Model for the geo_countries table
 */
class GeoCountriesModel extends Model
{
    protected $table      = 'geo_countries';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    protected $allowedFields = [
        'name',
        'country_code',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'country_code' => 'required|exact_length[2]|is_unique[geo_countries.country_code,id,{id}]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Country name is required',
            'max_length' => 'Country name cannot exceed 100 characters'
        ],
        'country_code' => [
            'required' => 'Country code is required',
            'exact_length' => 'Country code must be exactly 2 characters',
            'is_unique' => 'Country code already exists'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get country by code
     *
     * @param string $code
     * @return array|null
     */
    public function getCountryByCode($code)
    {
        return $this->where('country_code', $code)->first();
    }

    /**
     * Get all countries ordered by name
     *
     * @return array
     */
    public function getAllCountries()
    {
        return $this->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Search countries by name
     *
     * @param string $search
     * @return array
     */
    public function searchCountries($search)
    {
        return $this->like('name', $search)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }
}
