<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('applications_profiling_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('applications_profiling_exercise/exercise/' . ($position['exercise_id'] ?? '#')) ?>"><?= esc($position['exercise_name'] ?? 'Exercise') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('applications_profiling_exercise/group/' . ($position['position_group_id'] ?? '#')) ?>"><?= esc($position['group_name'] ?? 'Group') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('applications_profiling_exercise/position/' . ($position['id'] ?? '#')) ?>"><?= esc($position['designation'] ?? 'Position') ?></a></li>
            <li class="breadcrumb-item active" aria-current="page">Employee Profile</li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary">Employee Profile: <?= esc($application['fname'] . ' ' . $application['lname']) ?></h1>
            <div>
                <a href="<?= base_url('applications_profiling_exercise/position/' . ($position['id'] ?? '#')) ?>" class="btn btn-sm btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i> Back to Applications
                </a>
                
                <?php if (empty($application['profile_status']) || $application['profile_status'] != 'completed'): ?>
                <button id="updateProfileStatus" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#profileStatusModal">
                    <i class="fas fa-check-circle me-1"></i> Update Profile Status
                </button>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <!-- Profile Status -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="alert <?= !empty($application['profile_status']) && $application['profile_status'] == 'completed' ? 'alert-success' : 'alert-info' ?>">
                        <strong>Profile Status:</strong> 
                        <?php 
                        $statusText = 'Not Started';
                        if (!empty($application['profile_status'])) {
                            switch($application['profile_status']) {
                                case 'completed':
                                    $statusText = 'Completed';
                                    break;
                                case 'in_progress':
                                    $statusText = 'In Progress';
                                    break;
                                case 'pending':
                                    $statusText = 'Pending';
                                    break;
                                default:
                                    $statusText = ucfirst($application['profile_status']);
                            }
                        }
                        echo $statusText;
                        ?>
                        <?php if (!empty($application['profiled_at'])): ?>
                            | <strong>Profiled On:</strong> <?= date('d M Y H:i', strtotime($application['profiled_at'])) ?>
                        <?php endif; ?>
                        <?php if (!empty($application['profiled_by'])): ?>
                            | <strong>Profiled By:</strong> <?= esc($application['profiled_by_name'] ?? 'User ID: ' . $application['profiled_by']) ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" 
                            data-bs-target="#personal" type="button" role="tab" 
                            aria-controls="personal" aria-selected="true">
                        Personal Information
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="education-tab" data-bs-toggle="tab" 
                            data-bs-target="#education" type="button" role="tab" 
                            aria-controls="education" aria-selected="false">
                        Education
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="experience-tab" data-bs-toggle="tab" 
                            data-bs-target="#experience" type="button" role="tab" 
                            aria-controls="experience" aria-selected="false">
                        Work Experience
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="files-tab" data-bs-toggle="tab" 
                            data-bs-target="#files" type="button" role="tab" 
                            aria-controls="files" aria-selected="false">
                        Uploaded Files
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content p-3 border border-top-0 rounded-bottom" id="profileTabsContent">
                <!-- Personal Information Tab -->
                <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                    <div class="row">
                        <div class="col-md-3 text-center mb-4">
                            <?php if (!empty($application['id_photo_path'])): ?>
                                <img src="<?= base_url($application['id_photo_path']) ?>" alt="Applicant Photo" class="img-fluid rounded mb-3" style="max-height: 200px;">
                            <?php else: ?>
                                <div class="border rounded p-4 bg-light mb-3">
                                    <i class="fas fa-user-circle fa-5x text-secondary"></i>
                                    <p class="mt-2 text-muted">No photo available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h5>Basic Information</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="40%">Full Name</th>
                                            <td><?= esc($application['fname'] . ' ' . $application['lname']) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Gender</th>
                                            <td><?= esc($application['gender'] ?? 'Not specified') ?></td>
                                        </tr>
                                        <tr>
                                            <th>Date of Birth</th>
                                            <td><?= !empty($application['dobirth']) ? date('d M Y', strtotime($application['dobirth'])) : 'Not specified' ?></td>
                                        </tr>
                                        <tr>
                                            <th>Place of Origin</th>
                                            <td><?= esc($application['place_of_origin'] ?? 'Not specified') ?></td>
                                        </tr>
                                        <tr>
                                            <th>Citizenship</th>
                                            <td><?= esc($application['citizenship'] ?? 'Not specified') ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5>Contact Information</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="40%">Contact Details</th>
                                            <td><?= esc($application['contact_details'] ?? 'Not provided') ?></td>
                                        </tr>
                                        <tr>
                                            <th>Address</th>
                                            <td><?= esc($application['location_address'] ?? 'Not provided') ?></td>
                                        </tr>
                                    </table>
                                    
                                    <h5 class="mt-4">Current Employment</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="40%">Current Employer</th>
                                            <td><?= esc($application['current_employer'] ?? 'Not provided') ?></td>
                                        </tr>
                                        <tr>
                                            <th>Current Position</th>
                                            <td><?= esc($application['current_position'] ?? 'Not provided') ?></td>
                                        </tr>
                                        <tr>
                                            <th>Current Salary</th>
                                            <td><?= esc($application['current_salary'] ?? 'Not provided') ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <?php if (!empty($application['publications']) || !empty($application['awards'])): ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <h5>Professional Achievements</h5>
                                    <?php if (!empty($application['publications'])): ?>
                                    <h6 class="mt-3">Publications</h6>
                                    <p><?= nl2br(esc($application['publications'])) ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($application['awards'])): ?>
                                    <h6 class="mt-3">Awards and Recognitions</h6>
                                    <p><?= nl2br(esc($application['awards'])) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Education Tab -->
                <div class="tab-pane fade" id="education" role="tabpanel" aria-labelledby="education-tab">
                    <?php if (empty($education)): ?>
                        <div class="alert alert-info">No education records found for this applicant.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Level</th>
                                        <th>Institution</th>
                                        <th>Course/Program</th>
                                        <th>Duration</th>
                                        <th>Units/Grade</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($education as $edu): ?>
                                        <tr>
                                            <td>
                                                <?= esc($educationModel->getEducationLevelText($edu['education_level'])) ?>
                                            </td>
                                            <td><?= esc($edu['institution']) ?></td>
                                            <td><?= esc($edu['course']) ?></td>
                                            <td>
                                                <?= date('M Y', strtotime($edu['date_from'])) ?> - 
                                                <?= !empty($edu['date_to']) ? date('M Y', strtotime($edu['date_to'])) : 'Present' ?>
                                            </td>
                                            <td><?= esc($edu['units'] ?? 'N/A') ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Work Experience Tab -->
                <div class="tab-pane fade" id="experience" role="tabpanel" aria-labelledby="experience-tab">
                    <?php if (empty($experiences)): ?>
                        <div class="alert alert-info">No work experience records found for this applicant.</div>
                    <?php else: ?>
                        <?php 
                        // Calculate total years of experience if available
                        $totalYears = 0;
                        if (isset($experienceSummary) && isset($experienceSummary->total_years)) {
                            $totalYears = $experienceSummary->total_years;
                        }
                        
                        if ($totalYears > 0): 
                        ?>
                            <div class="alert alert-primary mb-4">
                                <i class="fas fa-info-circle me-2"></i> Total Work Experience: <strong><?= number_format($totalYears, 1) ?> years</strong>
                            </div>
                        <?php endif; ?>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Employer</th>
                                        <th>Position</th>
                                        <th>Duration</th>
                                        <th>Description</th>
                                        <th>Achievements</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($experiences as $exp): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($exp['employer']) ?></strong>
                                                <?php if (!empty($exp['employer_contacts_address'])): ?>
                                                    <div class="small text-muted mt-1"><?= esc($exp['employer_contacts_address']) ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= esc($exp['position']) ?></td>
                                            <td>
                                                <?= date('M Y', strtotime($exp['date_from'])) ?> - 
                                                <?= !empty($exp['date_to']) ? date('M Y', strtotime($exp['date_to'])) : 'Present' ?>
                                                
                                                <?php
                                                // Calculate duration for this position
                                                $from = new DateTime($exp['date_from']);
                                                $to = !empty($exp['date_to']) ? new DateTime($exp['date_to']) : new DateTime();
                                                $interval = $from->diff($to);
                                                $years = $interval->y + ($interval->m / 12);
                                                ?>
                                                
                                                <div class="small text-muted mt-1">
                                                    (<?= number_format($years, 1) ?> years)
                                                </div>
                                            </td>
                                            <td>
                                                <?php if (!empty($exp['work_description'])): ?>
                                                    <?= nl2br(esc($exp['work_description'])) ?>
                                                <?php else: ?>
                                                    <em class="text-muted">No description provided</em>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($exp['achievements'])): ?>
                                                    <?= nl2br(esc($exp['achievements'])) ?>
                                                <?php else: ?>
                                                    <em class="text-muted">None specified</em>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Files Tab -->
                <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                    <?php if (empty($files)): ?>
                        <div class="alert alert-info">No files uploaded by this applicant.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="25%">File Title</th>
                                        <th width="40%">Description</th>
                                        <th width="15%">Uploaded</th>
                                        <th width="15%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($files as $index => $file): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td><?= esc($file['file_title']) ?></td>
                                            <td>
                                                <?php if (!empty($file['file_description'])): ?>
                                                    <?= nl2br(esc($file['file_description'])) ?>
                                                <?php else: ?>
                                                    <em class="text-muted">No description</em>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d M Y', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-primary" target="_blank">
                                                    <i class="fas fa-eye me-1"></i> View
                                                </a>
                                                <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-secondary" download>
                                                    <i class="fas fa-download me-1"></i> Download
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Status Update Modal -->
<div class="modal fade" id="profileStatusModal" tabindex="-1" aria-labelledby="profileStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="profileStatusModalLabel">Update Profile Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="profileStatusForm" action="<?= base_url('applications_profiling_exercise/update_profile_status/' . $application['id']) ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="profile_status" class="form-label">Profile Status</label>
                        <select class="form-select" id="profile_status" name="profile_status" required>
                            <option value="">Select Status</option>
                            <option value="pending" <?= ($application['profile_status'] ?? '') == 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="in_progress" <?= ($application['profile_status'] ?? '') == 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="completed" <?= ($application['profile_status'] ?? '') == 'completed' ? 'selected' : '' ?>>Completed</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="profile_details" class="form-label">Details/Notes</label>
                        <textarea class="form-control" id="profile_details" name="profile_details" rows="3"><?= esc($application['profile_details'] ?? '') ?></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveProfileStatus">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Handle profile status form submission
    $('#saveProfileStatus').on('click', function() {
        $('#profileStatusForm').submit();
    });
    
    // Initialize the tabs if needed
    var triggerTabList = [].slice.call(document.querySelectorAll('#profileTabs button'))
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
    
    // Set active tab based on URL hash if present
    let hash = window.location.hash;
    if (hash) {
        $('.nav-tabs button[data-bs-target="' + hash + '"]').tab('show');
    }
    
    // Update URL hash when tab changes
    $('.nav-tabs button').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.getAttribute('data-bs-target');
    });
});
</script>
<?= $this->endSection() ?> 