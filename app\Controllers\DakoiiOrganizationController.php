<?php

namespace App\Controllers;

class DakoiiOrganizationController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    // Organization Methods
    public function organizationList()
    {
        // Using dummy data for UI development
        $organizations = [
            [
                'id' => 1,
                'org_code' => 'DIT',
                'org_name' => 'Department of Information Technology',
                'description' => 'Government IT Department',
                'is_active' => 1,
                'license_status' => 'active'
            ],
            [
                'id' => 2,
                'org_code' => 'DOW',
                'org_name' => 'Department of Works',
                'description' => 'Infrastructure Development',
                'is_active' => 1,
                'license_status' => 'active'
            ]
        ];

        $data['title'] = "Organizations";
        $data['menu'] = "organizations";
        $data['organizations'] = $organizations;
        echo view('dakoii/dakoii_organizations', $data);
    }

    public function organizationCreateForm()
    {
        $data['title'] = "Create Organization";
        $data['menu'] = "organizations";
        echo view('dakoii/dakoii_organization_create', $data);
    }

    public function organizationStore()
    {
        if (!$this->validate(['org_name' => 'required'])) {
            session()->setFlashdata('error', 'Enter valid Data');
            return redirect()->to('dakoii/dashboard');
        }

        $orgcode = rand(11111, 99999);
        // For UI development - simulate unique orgcode check

        $data = [
            'org_code' => $orgcode,
            'org_name' => $this->request->getVar('org_name'),
            'description' => $this->request->getVar('description'),
            'is_active' => 1,
        ];

        // Simulate successful insert for UI development

        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile->isValid() && $logoFile->getSize() > 0) {
            // Make sure the upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            $logoFile->move($uploadPath, $newName);

            // Store only the relative path without base_url
            $data['orglogo'] = 'public/uploads/org_logo/' . $newName;

            // For UI development - simulate update
            // Log the upload for debugging
            log_message('info', 'Organization logo uploaded: ' . $data['orglogo']);
        }

        session()->setFlashdata('success', 'Organization Created');
        return redirect()->to(base_url('dakoii/organization/view/' . $orgcode));
    }

    public function organizationView($orgcode)
    {
        // Using dummy organization data for UI development
        $org = [
            'id' => 1,
            'org_code' => $orgcode,
            'org_name' => 'Department of Information Technology',
            'description' => 'Government IT Department',
            'location_lock_country' => 1,
            'location_lock_province' => 1,
            'is_active' => 1,
            'license_status' => 'active'
        ];

        $data['title'] = "Organization Details";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Using dummy admin data
        $data['admins'] = [
            [
                'id' => 1,
                'name' => 'John Doe',
                'username' => 'admin',
                'role' => 'admin',
                'orgcode' => $orgcode
            ]
        ];

        // Using dummy geographic data
        $data['country_name'] = 'Papua New Guinea';
        $data['province_name'] = 'National Capital District';
        $data['set_country'] = ['id' => 1, 'name' => 'Papua New Guinea'];
        $data['get_provinces'] = [
            ['id' => 1, 'name' => 'National Capital District'],
            ['id' => 2, 'name' => 'Western Province']
        ];

        // Using dummy exercise data
        $data['exercises'] = [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'status' => 'published',
                'org_id' => $org['id']
            ]
        ];

        // Check if the user has permission to add exercises
        $data['can_add_exercise'] = true; // Default to true, adjust based on your permission system

        echo view('dakoii/dakoii_open_org', $data);
    }

    public function organizationEditForm($orgcode)
    {
        // Using dummy organization data for UI development
        $org = [
            'id' => 1,
            'org_code' => $orgcode,
            'org_name' => 'Department of Information Technology',
            'description' => 'Government IT Department',
            'location_lock_country' => 1,
            'location_lock_province' => 1,
            'is_active' => 1,
            'license_status' => 'active'
        ];

        $data['title'] = "Edit Organization";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Using dummy geographic data
        $data['country_name'] = 'Papua New Guinea';
        $data['province_name'] = 'National Capital District';
        $data['set_country'] = ['id' => 1, 'name' => 'Papua New Guinea'];
        $data['get_provinces'] = [
            ['id' => 1, 'name' => 'National Capital District'],
            ['id' => 2, 'name' => 'Western Province']
        ];

        echo view('dakoii/dakoii_organization_edit', $data);
    }

    public function organizationUpdate()
    {
        if (!$this->validate(['org_name' => 'required'])) {
            session()->setFlashdata('error', 'Organization name is required');
            return redirect()->back();
        }

        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        $addprov = "";
        if (!empty($this->request->getVar('country'))) {
            $addprov = $this->request->getVar('province');
        }

        $data = [
            'org_name' => $this->request->getVar('org_name'),
            'description' => $this->request->getVar('description'),
            'location_lock_country' => $this->request->getVar('country'),
            'location_lock_province' => $addprov,
            'is_active' => $this->request->getVar('status'),
        ];

        // Process logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            // Make sure the upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Generate unique name and move the file
            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            $logoFile->move($uploadPath, $newName);

            // Set the relative path for storage
            $data['logo_path'] = 'public/uploads/org_logo/' . $newName;

            // Log the upload for debugging
            log_message('info', 'Organization logo uploaded: ' . $data['logo_path']);
        }

        // For UI development - simulate successful update
        session()->setFlashdata('success', 'Organization Updated Successfully');
        return redirect()->back();
    }

    public function organizationUpdateLicense()
    {
        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        $data = [
            'license_status' => $this->request->getVar('license_status'),
        ];

        // For UI development - simulate successful update
        session()->setFlashdata('success', 'License Status Changed');
        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }
}
