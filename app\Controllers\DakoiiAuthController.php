<?php

namespace App\Controllers;

class DakoiiAuthController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    // Authentication Methods
    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";
        echo view('dakoii/dakoii_login', $data);
    }

    public function loginForm()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";
        echo view('dakoii/dakoii_login', $data);
    }

    public function processLogin()
    {
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];
        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter both username and password');
            return redirect()->to('dakoii');
        }

        $username = $this->request->getVar('username');
        $password = $this->request->getVar('password');

        // Using dummy user data for UI development
        if ($username === 'admin' && $password === 'password') {
            $user = [
                'id' => 1,
                'name' => 'System Administrator',
                'username' => 'admin',
                'role' => 'admin'
            ];

            $this->session->set([
                'logged_in' => true,
                'name' => $user['name'],
                'username' => $user['username'],
                'role' => $user['role'],
                'user_id' => $user['id']
            ]);

            session()->setFlashdata('success', 'Welcome back, ' . $user['name']);
            return redirect()->to('dakoii/dashboard');
        } else {
            session()->setFlashdata('error', 'Invalid username or password');
            return redirect()->to('dakoii');
        }
    }

    public function adminLogout()
    {
        $session = session();
        $session->destroy();
        return redirect()->to(base_url());
    }
}
