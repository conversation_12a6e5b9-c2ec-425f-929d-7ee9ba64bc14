<?php
/**
 * View file for listing positions in a position group
 * 
 * @var array $exercise The exercise details
 * @var array $positionGroup The position group details
 * @var array $positions List of positions in this group
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('applications_pre_screening_exercise') ?>">Exercises</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('applications_pre_screening_exercise/view/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($positionGroup['group_name']) ?></li>
                </ol>
            </nav>
            <h2><i class="fas fa-briefcase me-2"></i>Positions</h2>
            <p class="text-muted">
                Positions in group: <strong><?= esc($positionGroup['group_name']) ?></strong><br>
                <small>Exercise: <?= esc($exercise['exercise_name']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('applications_pre_screening_exercise/view/' . $exercise['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Position Groups
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No positions found in this group.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="35%">Position Title</th>
                                <th width="15%">Classification</th>
                                <th width="15%">Total Applications</th>
                                <th width="15%">Pending Pre-screening</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($position['designation']) ?></td>
                                    <td><?= esc($position['classification']) ?></td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?= $position['applications_count'] ?? 0 ?> Applications
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (($position['pending_count'] ?? 0) > 0): ?>
                                            <span class="badge bg-warning">
                                                <?= $position['pending_count'] ?> Pending
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success">All Complete</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('applications_pre_screening_exercise/position/' . $position['id']) ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-users"></i> View Applicants
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by ID asc
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions available in this group",
        }
    });
});
</script>
<?= $this->endSection() ?> 