<?php

namespace App\Controllers;

class AdminProfileController extends BaseController
{
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();

        // Load helpers
        helper(['url', 'form', 'GeminiAI']);
    }

    /**
     * Display the list of exercises for profiling.
     */
    public function index()
    {
        // Dummy data for UI development - replace with actual model calls later
        $data['exercises'] = [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'status' => 'selection',
                'group_count' => 3,
                'position_count' => 8,
                'applicant_count' => 25
            ],
            [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment',
                'status' => 'published',
                'group_count' => 2,
                'position_count' => 5,
                'applicant_count' => 18
            ],
            [
                'id' => 3,
                'exercise_name' => 'Administrative Positions 2024',
                'status' => 'selection',
                'group_count' => 4,
                'position_count' => 12,
                'applicant_count' => 42
            ]
        ];

        // Log exercise details for debugging
        foreach ($data['exercises'] as $exercise) {
            log_message('debug', "Exercise ID: {$exercise['id']}, Name: {$exercise['exercise_name']}, Status: {$exercise['status']}");
        }

        $data['title'] = 'Profiling Exercises';
        $data['menu'] = 'profiling'; // For active menu highlighting

        return view('profiles/profiles_exercise_list', $data);
    }

    /**
     * Display position groups within a specific exercise.
     * @param int $exerciseId
     */
    public function profilePositionGroups($exerciseId)
    {
        // Dummy data for UI development - replace with actual model calls later
        $data['position_groups'] = [
            [
                'id' => 1,
                'exercise_id' => $exerciseId,
                'group_name' => 'Senior IT Positions',
                'position_count' => 3,
                'applicant_count' => 12
            ],
            [
                'id' => 2,
                'exercise_id' => $exerciseId,
                'group_name' => 'Junior IT Positions',
                'position_count' => 2,
                'applicant_count' => 8
            ],
            [
                'id' => 3,
                'exercise_id' => $exerciseId,
                'group_name' => 'Support Positions',
                'position_count' => 3,
                'applicant_count' => 5
            ]
        ];

        $data['exercise'] = [
            'id' => $exerciseId,
            'exercise_name' => 'IT Recruitment Exercise 2024'
        ];

        // Ensure exercise name is never empty
        if (empty($data['exercise']) || empty($data['exercise']['exercise_name'])) {
            log_message('error', "Exercise not found or name is empty for ID: $exerciseId");
            $data['exercise'] = ['id' => $exerciseId, 'exercise_name' => 'Exercise ' . $exerciseId];
        }

        $data['title'] = 'Position Groups for ' . $data['exercise']['exercise_name'];
        $data['menu'] = 'profiling';

        return view('profiles/profiles_position_group_list', $data);
    }

    /**
     * Display positions within a specific position group.
     * @param int $groupId
     */
    public function profilePositions($groupId)
    {
        // Dummy data for UI development - replace with actual model calls later
        $data['positions'] = [
            [
                'id' => 1,
                'position_group_id' => $groupId,
                'designation' => 'Senior Software Engineer',
                'classification' => 'Level 8',
                'location' => 'Port Moresby',
                'annual_salary' => '85000',
                'applicant_count' => 5
            ],
            [
                'id' => 2,
                'position_group_id' => $groupId,
                'designation' => 'Software Developer',
                'classification' => 'Level 6',
                'location' => 'Lae',
                'annual_salary' => '65000',
                'applicant_count' => 4
            ],
            [
                'id' => 3,
                'position_group_id' => $groupId,
                'designation' => 'Junior Developer',
                'classification' => 'Level 4',
                'location' => 'Port Moresby',
                'annual_salary' => '45000',
                'applicant_count' => 3
            ]
        ];

        $data['position_group'] = [
            'id' => $groupId,
            'group_name' => 'Senior IT Positions',
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024'
        ];

        // Fallback for missing group
        if (empty($data['position_group'])) {
            $data['position_group'] = ['id' => $groupId, 'group_name' => 'Group ' . $groupId];
        }

        $data['title'] = 'Positions in ' . $data['position_group']['group_name'];
        $data['menu'] = 'profiling';

        return view('profiles/profiles_position_list', $data);
    }

    /**
     * Display applicants for a specific position who passed pre-screening.
     * @param int $positionId
     */
    public function profileApplicants($positionId)
    {
        // Dummy data for UI development - replace with actual model calls later
        $data['position'] = [
            'id' => $positionId,
            'designation' => 'Senior Software Engineer',
            'classification' => 'Level 8',
            'location' => 'Port Moresby',
            'annual_salary' => '85000',
            'position_group_id' => 1,
            'group_name' => 'Senior IT Positions',
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024'
        ];

        // Handle case where position is not found
        if (empty($data['position'])) {
            log_message('error', "Position not found for ID: $positionId");
            $data['position'] = ['id' => $positionId, 'designation' => 'Position ' . $positionId];
        }

        // Dummy applications data
        $data['applications'] = [
            [
                'id' => 1,
                'applicant_id' => 101,
                'position_id' => $positionId,
                'fname' => 'John',
                'lname' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'pre_screened_status' => 'passed',
                'pre_screened' => '2024-01-15 10:30:00',
                'application_number' => 'APP-2024-001'
            ],
            [
                'id' => 2,
                'applicant_id' => 102,
                'position_id' => $positionId,
                'fname' => 'Jane',
                'lname' => 'Smith',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'pre_screened_status' => 'passed',
                'pre_screened' => '2024-01-14 14:20:00',
                'application_number' => 'APP-2024-002'
            ],
            [
                'id' => 3,
                'applicant_id' => 103,
                'position_id' => $positionId,
                'fname' => 'Michael',
                'lname' => 'Johnson',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'pre_screened_status' => 'passed',
                'pre_screened' => '2024-01-13 16:45:00',
                'application_number' => 'APP-2024-003'
            ]
        ];

        $data['title'] = 'Profiling Applications for ' . ($data['position']['designation'] ?? 'Position');
        $data['menu'] = 'profiling';

        return view('profiles/profiles_applicant_list', $data);
    }
}
