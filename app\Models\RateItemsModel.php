<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * RateItemsModel
 *
 * Model for the rate_items table
 */
class RateItemsModel extends Model
{
    protected $table         = 'rate_items';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'item_label',
        'description',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'item_label' => 'required|max_length[150]'
    ];

    protected $validationMessages = [
        'item_label' => [
            'required'    => 'Item label is required',
            'max_length'  => 'Item label cannot exceed 150 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get item by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getItemById($id)
    {
        return $this->find($id);
    }

    /**
     * Get all active items
     *
     * @return array
     */
    public function getAllItems()
    {
        return $this->orderBy('item_label', 'ASC')->findAll();
    }

    /**
     * Search items by label
     *
     * @param string $search
     * @return array
     */
    public function searchItems($search)
    {
        return $this->like('item_label', $search)
                    ->orderBy('item_label', 'ASC')
                    ->findAll();
    }

    /**
     * Get items with their scores
     *
     * @return array
     */
    public function getItemsWithScores()
    {
        return $this->select('rate_items.*, COUNT(rate_items_scores.id) as score_count')
                    ->join('rate_items_scores', 'rate_items.id = rate_items_scores.item_id', 'left')
                    ->groupBy('rate_items.id')
                    ->orderBy('rate_items.item_label', 'ASC')
                    ->findAll();
    }

    /**
     * Get item statistics
     *
     * @return array
     */
    public function getItemStatistics()
    {
        $stats = [];
        
        // Total items
        $stats['total'] = $this->countAllResults(false);
        
        // Items with descriptions
        $stats['with_descriptions'] = $this->where('description IS NOT NULL')
                                           ->where('description !=', '')
                                           ->countAllResults(false);
        
        // Items with scores
        $itemsWithScores = $this->select('COUNT(DISTINCT rate_items.id) as count')
                                ->join('rate_items_scores', 'rate_items.id = rate_items_scores.item_id', 'inner')
                                ->first();
        
        $stats['with_scores'] = $itemsWithScores['count'] ?? 0;
        
        return $stats;
    }

    /**
     * Get items by creator
     *
     * @param int $createdBy
     * @return array
     */
    public function getItemsByCreator($createdBy)
    {
        return $this->where('created_by', $createdBy)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Check if item label exists
     *
     * @param string $label
     * @param int $excludeId
     * @return bool
     */
    public function labelExists($label, $excludeId = null)
    {
        $builder = $this->where('item_label', $label);
        
        if ($excludeId !== null) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() > 0;
    }

    /**
     * Get recently created items
     *
     * @param int $limit
     * @return array
     */
    public function getRecentItems($limit = 10)
    {
        return $this->orderBy('created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Update item description
     *
     * @param int $id
     * @param string $description
     * @param int $updatedBy
     * @return bool
     */
    public function updateDescription($id, $description, $updatedBy = null)
    {
        $data = ['description' => $description];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }
        
        return $this->update($id, $data);
    }
}
