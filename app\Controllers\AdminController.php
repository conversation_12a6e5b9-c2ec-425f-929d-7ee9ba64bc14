<?php

namespace App\Controllers;

class AdminController extends BaseController
{
    public $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    public function admin_dashboard()
    {
        // Check if user is logged in and has admin role
        if (!$this->session->get('logged_in') || $this->session->get('role') !== 'admin') {
            return redirect()->to(base_url())->with('error', 'Access denied. Admin privileges required.');
        }

        // Get current organization ID from session
        $org_id = $this->session->get('org_id');

        // Using dummy data for UI development
        $data = [
            'title' => 'Admin Dashboard',
            'menu' => 'dashboard',
            'total_users' => 25,
            'total_organizations' => 12,
            'total_provinces' => 22,
            'user_name' => $this->session->get('name'),
        ];

        return view('admin/admin_dashboard', $data);
    }
}