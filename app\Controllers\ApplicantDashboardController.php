<?php

namespace App\Controllers;

use App\Models\applicantsModel;

class ApplicantDashboardController extends BaseController
{
    protected $applicantsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
        $this->applicantsModel = new applicantsModel();
    }

    public function dashboard()
    {
        // Get applicant data
        $applicant_id = session()->get('applicant_id');
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('/')->with('error', 'Applicant not found');
        }

        // Initialize empty data for features that require missing models
        $data = [
            'title' => 'Applicant Dashboard',
            'menu' => 'dashboard',
            'applicant' => $applicant,
            'total_applications' => 0,
            'pending_applications' => 0,
            'shortlisted_applications' => 0,
            'rejected_applications' => 0,
            'recent_applications' => [],
            'latest_jobs' => []
        ];

        return view('applicant/applicant_dashboard', $data);
    }

    public function index()
    {
        return $this->dashboard();
    }
}
