<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Profiling Exercises') ?></h1>
            <!-- Optional: Add breadcrumbs or back button here -->
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <p class="mb-4">Select an exercise to view its position groups for profiling.</p>

            <?php if (!empty($exercises) && is_array($exercises)): ?>
                <div class="row">
                    <?php foreach ($exercises as $exercise): ?>
                        <div class="col-xl-3 col-md-6 mb-4">
                            <a href="<?= base_url('profile_applications_exercise/profile_exercise/' . esc($exercise['id'], 'url')) ?>" class="card border-left-primary shadow h-100 py-2 text-decoration-none hover-card">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Exercise</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= esc($exercise['exercise_name'] ?? 'Unnamed Exercise') ?></div>
                                            <div class="mt-2 small">
                                                <span class="badge bg-primary rounded-pill"><?= esc($exercise['group_count'] ?? 0) ?> Groups</span>
                                                <span class="badge bg-info rounded-pill"><?= esc($exercise['position_count'] ?? 0) ?> Positions</span>
                                                <span class="badge bg-success rounded-pill"><?= esc($exercise['applicant_count'] ?? 0) ?> Applications</span>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-folder-open fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info" role="alert">
                    No profiling exercises are currently available or defined.
                    <br><small>Note: Exercise data fetching is currently placeholder in the controller.</small>
                </div>
                <!-- Placeholder for demonstration -->
                <div class="row">
                     <div class="col-xl-3 col-md-6 mb-4">
                        <a href="<?= base_url('profile_applications_exercise/profile_exercise/1') ?>" class="card border-left-primary shadow h-100 py-2 text-decoration-none hover-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Exercise (Placeholder)</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">Sample Exercise 1</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-folder-open fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Add any specific JS for this page if needed -->
<script>
    // Example: Initialize tooltips if used
    // var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    // var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    //   return new bootstrap.Tooltip(tooltipTriggerEl)
    // })
</script>
<?= $this->endSection() ?>