<?php

namespace App\Controllers;

class ApplicationScreeningController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = \Config\Services::session();
    }

    /**
     * [GET] Display the list of applications pending pre-screening.
     * URI: /application_screening
     */
    public function index()
    {
        // Dummy data for UI development - replace with actual model calls later
        $applications = [
            [
                'id' => 1,
                'applicant_id' => 101,
                'position_id' => 1,
                'fname' => 'John',
                'lname' => 'Doe',
                'position_name' => 'Senior Software Engineer',
                'recieved_acknowledged' => '2024-01-15 10:30:00',
                'pre_screened' => null,
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'applicant_id' => 102,
                'position_id' => 2,
                'fname' => 'Jane',
                'lname' => 'Smith',
                'position_name' => 'Project Manager',
                'recieved_acknowledged' => '2024-01-14 14:20:00',
                'pre_screened' => null,
                'created_at' => '2024-01-09 11:15:00'
            ],
            [
                'id' => 3,
                'applicant_id' => 103,
                'position_id' => 3,
                'fname' => 'Michael',
                'lname' => 'Johnson',
                'position_name' => 'Financial Analyst',
                'recieved_acknowledged' => '2024-01-13 16:45:00',
                'pre_screened' => null,
                'created_at' => '2024-01-08 13:30:00'
            ]
        ];

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('application_pre_screening/application_pre_screening_list', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        // Dummy data for UI development - replace with actual model calls later
        $application = [
            'id' => $id,
            'applicant_id' => 101,
            'position_id' => 1,
            'designation' => 'Senior Software Engineer',
            'group_name' => 'IT Positions',
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'pre_screen_criteria' => json_encode([
                ['criteria' => 'Bachelor degree in Computer Science or related field', 'required' => true],
                ['criteria' => 'Minimum 3 years programming experience', 'required' => true],
                ['criteria' => 'Experience with web development frameworks', 'required' => false]
            ]),
            'recieved_acknowledged' => '2024-01-15 10:30:00',
            'pre_screened' => null,
            'created_at' => '2024-01-10 09:00:00'
        ];

        if (!$application) {
            return redirect()->to(base_url('application_screening'))
                ->with('error', 'Application not found.');
        }

        // Dummy applicant data
        $applicant = [
            'applicant_id' => 101,
            'fname' => 'John',
            'lname' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'address' => '123 Main Street, Port Moresby'
        ];

        // Dummy experiences data
        $experiences = [
            [
                'id' => 1,
                'company_name' => 'Tech Solutions PNG',
                'position' => 'Software Developer',
                'start_date' => '2020-01-01',
                'end_date' => '2023-12-31',
                'description' => 'Developed web applications using PHP and JavaScript'
            ],
            [
                'id' => 2,
                'company_name' => 'Digital Innovations',
                'position' => 'Junior Developer',
                'start_date' => '2018-06-01',
                'end_date' => '2019-12-31',
                'description' => 'Assisted in mobile app development projects'
            ]
        ];

        // Dummy education data
        $education = [
            [
                'id' => 1,
                'institution' => 'University of Papua New Guinea',
                'qualification' => 'Bachelor of Computer Science',
                'year_completed' => '2018',
                'grade' => 'Credit'
            ]
        ];

        // Dummy files data
        $files = [
            [
                'id' => 1,
                'file_type' => 'resume',
                'file_name' => 'john_doe_resume.pdf',
                'file_path' => '/uploads/resumes/john_doe_resume.pdf'
            ],
            [
                'id' => 2,
                'file_type' => 'certificate',
                'file_name' => 'degree_certificate.pdf',
                'file_path' => '/uploads/certificates/degree_certificate.pdf'
            ]
        ];

        // Get position and exercise data
        $position = [
            'id' => $application['position_id'],
            'designation' => $application['designation'] ?? 'N/A'
        ];

        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'] ?? 'N/A'
        ];

        // Parse pre-screening criteria
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            try {
                $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
            } catch (\Exception $e) {
                log_message('error', 'Error parsing pre-screening criteria: ' . $e->getMessage());
            }
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'applicant' => $applicant,
            'position' => $position,
            'exercise' => $exercise,
            'preScreenCriteria' => $preScreenCriteria,
            'experiences' => $experiences,
            'education' => $education,
            'files' => $files,
            'educationModel' => null // Remove model reference
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        // Dummy validation for UI development - replace with actual model calls later
        $application = ['id' => $id]; // Simulate finding application
        if (!$application) {
            return redirect()->to(base_url('application_screening'))
                ->with('error', 'Application not found.');
        }

        // Validate input
        $rules = [
            'status' => 'required|in_list[passed,failed,pending]',
            'remarks' => 'permit_empty|string|max_length[1000]'
        ];

        if ($this->request->getPost('status') === 'failed' && empty($this->request->getPost('remarks'))) {
            $rules['remarks'] = 'required|string|max_length[1000]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Process criteria results
        $criteriaResults = [];
        $criteriaIndices = $this->request->getPost('criteria_index') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        foreach ($criteriaIndices as $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update (dummy - not actually saving to database)
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id') ?? 1,
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id') ?? 1
        ];

        // Simulate successful update for UI development
        $updateSuccess = true; // Replace with actual model update call later

        if ($updateSuccess) {
            return redirect()->to(base_url("application_screening/show/{$id}"))
                ->with('success', 'Pre-screening results saved successfully.');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to save pre-screening results.');
        }
    }
}
