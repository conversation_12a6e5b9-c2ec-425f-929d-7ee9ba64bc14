<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">My Applications</h1>
            <p class="text-muted">View and track all your job applications</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if (empty($applications)): ?>
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">No Applications Yet</h5>
                                    <p class="mb-0">You haven't submitted any applications yet.
                                        <a href="<?= base_url('applicant/jobs') ?>" class="alert-link">Browse available positions</a> to get started.</p>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="applicationsTable">
                                <thead>
                                    <tr>
                                        <th>Application Number</th>
                                        <th>Position</th>
                                        <th>Organization</th>
                                        <th>Status</th>
                                        <th>Applied Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $data): ?>
                                        <tr>
                                            <td><?= esc($data['application']['application_number']) ?></td>
                                            <td><?= esc($data['position']['designation']) ?></td>
                                            <td><?= esc($data['organization']['org_name']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= get_status_color($data['application']['status']) ?>">
                                                    <?= ucfirst(esc($data['application']['status'])) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M d, Y', strtotime($data['application']['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?= base_url('applicant/application/' . $data['application']['id']) ?>"
                                                       class="btn btn-sm btn-primary"
                                                       title="View Application Details">
                                                        <i class="fas fa-file-alt"></i>
                                                    </a>
                                                    <a href="<?= base_url('applicant/jobs/position/' . $data['position']['id']) ?>"
                                                       class="btn btn-sm btn-info"
                                                       title="View Position Details">
                                                        <i class="fas fa-briefcase"></i>
                                                    </a>
                                                    <?php if (!empty($data['application']['remarks'])): ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-secondary"
                                                                data-bs-toggle="tooltip"
                                                                data-bs-placement="top"
                                                                title="<?= esc($data['application']['remarks']) ?>">
                                                            <i class="fas fa-comment"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        "responsive": true,
        "order": [[4, "desc"]], // Sort by Applied Date by default
        "pageLength": 10,
        "language": {
            "emptyTable": "No applications found"
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>