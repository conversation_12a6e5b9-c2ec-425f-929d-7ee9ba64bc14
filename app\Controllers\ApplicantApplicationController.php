<?php

namespace App\Controllers;

class ApplicantApplicationController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
    }

    public function uploadFile()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $file = $this->request->getFile('file');
        $title = $this->request->getPost('file_title');
        $description = $this->request->getPost('file_description');

        if (!$file || !$file->isValid() || $file->hasMoved()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid file upload'
            ]);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/applicant_files';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $file->getRandomName();

            // Move file to uploads directory
            if ($file->move($uploadPath, $newName)) {
                // Save file information to database
                $fileData = [
                    'applicant_id' => $applicant_id,
                    'title' => $title,
                    'description' => $description,
                    'file_path' => 'public/uploads/applicant_files/' . $newName,
                    'file_name' => $file->getName(),
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getMimeType(),
                    'created_by' => $applicant_id,
                    'updated_by' => $applicant_id
                ];

                // Simulate successful file save for UI development
                // Replace with actual model insert call later

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file' => $fileData
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error moving uploaded file'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteFile($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        // For UI development - simulate ownership verification
        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found or access denied'
            ]);
        }

        // For UI development - simulate successful file deletion
        return $this->response->setJSON([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    public function viewApplication($applicationId)
    {
        // Get current applicant ID from session
        $applicant_id = session()->get('applicant_id');

        // Using dummy data for UI development
        $application = [
            'id' => $applicationId,
            'applicant_id' => $applicant_id,
            'position_id' => 1,
            'status' => 'pending',
            'applied_date' => '2024-01-15',
            'cover_letter' => 'This is a sample cover letter for the application.'
        ];

        $position = [
            'id' => 1,
            'title' => 'Software Developer',
            'description' => 'Develop and maintain software applications',
            'requirements' => 'Bachelor degree in Computer Science',
            'org_id' => 1,
            'position_group_id' => 1
        ];

        $positionGroup = [
            'id' => 1,
            'name' => 'IT Positions',
            'exercise_id' => 1
        ];

        $organization = [
            'id' => 1,
            'org_name' => 'Tech Solutions Inc.',
            'description' => 'Leading technology company'
        ];

        $exercise = [
            'id' => 1,
            'title' => 'Annual Recruitment 2024',
            'description' => 'Annual recruitment exercise for various positions'
        ];

        // Dummy data for UI development - replace with actual model calls later
        $files = [
            [
                'id' => 1,
                'applicant_id' => $applicant_id,
                'title' => 'Resume',
                'description' => 'Updated resume document',
                'file_path' => 'public/uploads/applicant_files/resume.pdf',
                'file_name' => 'resume.pdf',
                'file_size' => 1024000,
                'file_type' => 'application/pdf',
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'applicant_id' => $applicant_id,
                'title' => 'Cover Letter',
                'description' => 'Application cover letter',
                'file_path' => 'public/uploads/applicant_files/cover_letter.pdf',
                'file_name' => 'cover_letter.pdf',
                'file_size' => 512000,
                'file_type' => 'application/pdf',
                'created_at' => '2024-01-14 14:20:00'
            ]
        ];

        // Dummy experiences data
        $experiences = [
            [
                'id' => 1,
                'applicant_id' => $applicant_id,
                'company_name' => 'Tech Solutions PNG',
                'position' => 'Software Developer',
                'date_from' => '2020-01-01',
                'date_to' => '2023-12-31',
                'description' => 'Developed web applications using PHP and JavaScript',
                'current_position' => 0
            ],
            [
                'id' => 2,
                'applicant_id' => $applicant_id,
                'company_name' => 'Digital Innovations',
                'position' => 'Junior Developer',
                'date_from' => '2018-06-01',
                'date_to' => '2019-12-31',
                'description' => 'Assisted in mobile app development projects',
                'current_position' => 0
            ]
        ];

        // Dummy education data
        $education = [
            [
                'id' => 1,
                'applicant_id' => $applicant_id,
                'institution' => 'University of Papua New Guinea',
                'qualification' => 'Bachelor of Computer Science',
                'date_from' => '2014-01-01',
                'date_to' => '2017-12-31',
                'grade' => 'Credit',
                'education_level_id' => 5
            ]
        ];

        $totalExperience = 4;
        $highestEducation = 'Bachelor of Computer Science';

        // Prepare data for the view
        $data = [
            'title' => 'Application Details',
            'menu' => 'applications',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'organization' => $organization,
            'exercise' => $exercise,
            'files' => $files,
            'experiences' => $experiences,
            'education' => $education,
            'totalExperience' => $totalExperience,
            'highestEducation' => $highestEducation,
            'educationLevels' => [
                1 => 'Primary',
                2 => 'Secondary',
                3 => 'Certificate',
                4 => 'Diploma',
                5 => 'Bachelor\'s Degree',
                6 => 'Master\'s Degree',
                7 => 'Doctorate'
            ]
        ];

        // Render the view
        return view('applicant/applicant_application_details', $data);
    }

    public function applications()
    {
        $applicant_id = session()->get('applicant_id');

        // Using dummy data for UI development
        $applications = [
            [
                'id' => 1,
                'position_title' => 'Software Developer',
                'organization' => 'Tech Solutions Inc.',
                'applied_date' => '2024-01-15',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'position_title' => 'Project Manager',
                'organization' => 'Business Corp',
                'applied_date' => '2024-01-10',
                'status' => 'shortlisted'
            ]
        ];

        $data = [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('applicant/applicant_applications', $data);
    }
}
