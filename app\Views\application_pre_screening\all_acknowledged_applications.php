<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h1 class="card-title"><?= $title ?></h1>
        </div>
        <div class="card-body">
            <?php if (session()->has('success')): ?>
                <div class="alert alert-success"><?= session('success') ?></div>
            <?php endif; ?>
            
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger"><?= session('error') ?></div>
            <?php endif; ?>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="applications-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Applicant Name</th>
                            <th>Position</th>
                            <th>Submission Date</th>
                            <th>Acknowledged Date</th>
                            <th>Pre-Screened</th>
                            <th>Pre-Screened Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($applications as $app): ?>
                            <tr>
                                <td><?= $app['id'] ?></td>
                                <td><?= $app['applicant_name'] ?></td>
                                <td><?= $app['position_id'] ?></td>
                                <td><?= $app['submitted_date'] ?></td>
                                <td><?= $app['recieved_acknowledged'] ?></td>
                                <td><?= $app['pre_screened'] ?: 'Not Pre-Screened' ?></td>
                                <td>
                                    <?php if ($app['pre_screened_status']): ?>
                                        <span class="badge <?= $app['pre_screened_status'] == 'Accepted' ? 'bg-success' : 'bg-danger' ?>">
                                            <?= $app['pre_screened_status'] ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Pending</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?= base_url('applications_pre_screening/view_application/' . $app['id']) ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    <?php if (empty($app['pre_screened'])): ?>
                                    <button type="button" class="btn btn-sm btn-primary pre-screen-btn" data-id="<?= $app['id'] ?>">
                                        <i class="fas fa-check"></i> Pre-Screen
                                    </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Pre-Screen Modal -->
<div class="modal fade" id="preScreenModal" tabindex="-1" role="dialog" aria-labelledby="preScreenModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="preScreenModalLabel">Pre-Screen Application</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="preScreenForm">
                <div class="modal-body">
                    <input type="hidden" name="application_id" id="application_id">
                    <?= csrf_field() ?>
                    
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="">Select Status</option>
                            <option value="Accepted">Accepted</option>
                            <option value="Rejected">Rejected</option>
                        </select>
                    </div>
                    
                    <div class="form-group mt-3">
                        <label for="remarks">Remarks</label>
                        <textarea name="remarks" id="remarks" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applications-table').DataTable();
    
    // Pre-Screen Button Click
    $('.pre-screen-btn').on('click', function() {
        var id = $(this).data('id');
        $('#application_id').val(id);
        $('#preScreenModal').modal('show');
    });
    
    // Pre-Screen Form Submit
    $('#preScreenForm').on('submit', function(e) {
        e.preventDefault();
        
        var id = $('#application_id').val();
        var status = $('#status').val();
        var remarks = $('#remarks').val();
        
        $.ajax({
            url: '<?= base_url('applications_pre_screening/pre_screen') ?>/' + id,
            type: 'POST',
            data: {
                status: status,
                remarks: remarks,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    $('#preScreenModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.message);
                }
                // Update CSRF hash
                $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    });
});
</script>

<?= $this->endSection() ?> 