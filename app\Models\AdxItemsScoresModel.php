<?php

namespace App\Models;

use CodeIgniter\Model;

class AdxItemsScoresModel extends Model
{
    protected $table         = 'adx_items_scores';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    // Fields that are allowed to be set during insert/update operations
    protected $allowedFields = [
        'item_id',
        'score',
        'score_description',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    // Validation
    protected $validationRules = [
        'item_id'           => 'required|numeric',
        'score'             => 'required|numeric',
        'score_description' => 'permit_empty|max_length[255]'
    ];
    
    protected $validationMessages = [
        'item_id' => [
            'required' => 'Item ID is required',
            'numeric'  => 'Item ID must be a number'
        ],
        'score' => [
            'required' => 'Score is required',
            'numeric'  => 'Score must be a number'
        ],
        'score_description' => [
            'max_length' => 'Score description cannot exceed 255 characters'
        ]
    ];
    
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    
    /**
     * Get score by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getScoreById($id)
    {
        return $this->find($id);
    }
    
    /**
     * Get all scores for a specific item
     *
     * @param int $itemId
     * @return array
     */
    public function getScoresByItemId($itemId)
    {
        return $this->where('item_id', $itemId)->findAll();
    }
    
    /**
     * Get item with its scores
     *
     * @param int $itemId
     * @return array
     */
    public function getItemWithScores($itemId)
    {
        $itemModel = new AdxItemsModel();
        $item = $itemModel->find($itemId);
        
        if ($item) {
            $item['scores'] = $this->getScoresByItemId($itemId);
        }
        
        return $item;
    }
}
