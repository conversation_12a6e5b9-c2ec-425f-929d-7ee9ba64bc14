<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationEducationModel
 *
 * Model for the appx_application_education table
 */
class AppxApplicationEducationModel extends Model
{
    protected $table         = 'appx_application_education';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id', 'applicant_id', 'institution', 'course',
        'date_from', 'date_to', 'education_level', 'units',
        'created_by', 'updated_by', 'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'application_id'  => 'required|numeric',
        'applicant_id'    => 'required|numeric',
        'institution'     => 'required|max_length[255]',
        'course'          => 'required|max_length[255]',
        'date_from'       => 'required|valid_date'
    ];

    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'institution' => [
            'required'    => 'Institution name is required',
            'max_length'  => 'Institution name cannot exceed 255 characters'
        ],
        'course' => [
            'required'    => 'Course is required',
            'max_length'  => 'Course cannot exceed 255 characters'
        ],
        'date_from' => [
            'required'    => 'Start date is required',
            'valid_date'  => 'Start date must be a valid date'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Education level constants
     */
    const LEVEL_PRIMARY = 1;
    const LEVEL_SECONDARY = 2;
    const LEVEL_CERTIFICATE = 3;
    const LEVEL_DIPLOMA = 4;
    const LEVEL_BACHELORS = 5;
    const LEVEL_MASTERS = 6;
    const LEVEL_DOCTORATE = 7;

    /**
     * Get education level text
     *
     * @param int $level
     * @return string
     */
    public function getEducationLevelText($level)
    {
        $levels = [
            self::LEVEL_PRIMARY => 'Primary',
            self::LEVEL_SECONDARY => 'Secondary',
            self::LEVEL_CERTIFICATE => 'Certificate',
            self::LEVEL_DIPLOMA => 'Diploma',
            self::LEVEL_BACHELORS => 'Bachelor\'s Degree',
            self::LEVEL_MASTERS => 'Master\'s Degree',
            self::LEVEL_DOCTORATE => 'Doctorate'
        ];

        return isset($levels[$level]) ? $levels[$level] : 'Unknown';
    }

    /**
     * Get education by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getEducationById($id)
    {
        return $this->find($id);
    }

    /**
     * Get education records by application ID
     *
     * @param int $applicationId
     * @return array
     */
    public function getEducationByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)
                    ->orderBy('education_level', 'DESC')
                    ->orderBy('date_from', 'DESC')
                    ->findAll();
    }

    /**
     * Get education records by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getEducationByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)
                    ->orderBy('education_level', 'DESC')
                    ->orderBy('date_from', 'DESC')
                    ->findAll();
    }

    /**
     * Get highest education level
     *
     * @param int $applicationId
     * @return array|null
     */
    public function getHighestEducation($applicationId)
    {
        return $this->where('application_id', $applicationId)
                    ->orderBy('education_level', 'DESC')
                    ->first();
    }

    /**
     * Check if applicant has a specific education level or higher
     *
     * @param int $applicationId
     * @param int $level
     * @return bool
     */
    public function hasEducationLevel($applicationId, $level)
    {
        $count = $this->where('application_id', $applicationId)
                      ->where('education_level >=', $level)
                      ->countAllResults();

        return $count > 0;
    }
}