<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<style>
    .image-preview {
        max-width: 200px;
        max-height: 100px;
        object-fit: contain;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        padding: 0.5rem;
        background: #fff;
    }
    .preview-container {
        text-align: center;
        margin-bottom: 1rem;
    }
    .preview-container .no-image {
        width: 200px;
        height: 100px;
        border: 2px dashed #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        margin: 0 auto 1rem;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="bg-white rounded shadow-sm p-4">
                <h5 class="mb-4 text-primary">Organization Profile</h5>
                
                <?php if (session()->has('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session()->get('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach ?>
                        </ul>
                    </div>
                <?php endif ?>

                <form action="<?= base_url('settings/organization/update') ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    
                    <!-- Organization Logo -->
                    <div class="mb-4 text-center">
                        <?php if (!empty($org_data['orglogo'])): ?>
                            <img src="<?= base_url($org_data['orglogo']) ?>" 
                                 alt="Organization Logo" 
                                 class="img-thumbnail mb-3" 
                                 style="max-width: 200px;">
                        <?php else: ?>
                            <div class="mb-3">
                                <i class="fas fa-building fa-5x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <label for="orglogo" class="form-label">Update Organization Logo</label>
                            <input type="file" class="form-control" id="orglogo" name="orglogo" accept="image/*">
                            <small class="text-muted">Recommended size: 200x200px, Max size: 10MB</small>
                        </div>
                    </div>

                    <div class="row g-3">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="orgcode" class="form-label">Organization Code *</label>
                                <input type="text" class="form-control" id="orgcode" name="orgcode" 
                                       value="<?= old('orgcode', $org_data['orgcode'] ?? '') ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Organization Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', $org_data['name'] ?? '') ?>" required>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" required><?= old('description', $org_data['description'] ?? '') ?></textarea>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">Contact Information</h6>
                        </div>

                        <div class="col-12">
                            <div class="mb-3">
                                <label for="postal_address" class="form-label">Postal Address *</label>
                                <textarea class="form-control" id="postal_address" name="postal_address" 
                                          rows="2" required><?= old('postal_address', $org_data['postal_address'] ?? '') ?></textarea>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phones" class="form-label">Phone Numbers *</label>
                                <input type="text" class="form-control" id="phones" name="phones"
                                       placeholder="Separate multiple numbers with commas"
                                       value="<?= old('phones', $org_data['phones'] ?? '') ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emails" class="form-label">Email Addresses *</label>
                                <input type="text" class="form-control" id="emails" name="emails"
                                       placeholder="Separate multiple emails with commas"
                                       value="<?= old('emails', $org_data['emails'] ?? '') ?>" required>
                            </div>
                        </div>

                        <!-- Signature and Stamp Section -->
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">Signature and Stamp</h6>
                        </div>

                        <!-- Signature -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Digital Signature</h6>
                                    <div class="preview-container">
                                        <?php if (!empty($org_data['signature_filepath'])): ?>
                                            <img src="<?= base_url($org_data['signature_filepath']) ?>" 
                                                 alt="Signature" class="image-preview" id="signaturePreview">
                                            <button type="button" class="btn btn-sm btn-danger mb-3" onclick="removeSignature()">
                                                <i class="fas fa-trash"></i> Remove Signature
                                            </button>
                                        <?php else: ?>
                                            <div class="no-image" id="signaturePreview">
                                                <span><i class="fas fa-signature fa-2x"></i></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-3">
                                        <label for="signature_filepath" class="form-label">Upload Signature</label>
                                        <input type="file" class="form-control" id="signature_filepath" 
                                               name="signature_filepath" accept="image/*" onchange="previewImage(this, 'signaturePreview')">
                                        <small class="text-muted">Upload signature image (PNG or JPG, max 10MB)</small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="signature_name" class="form-label">Signatory Name</label>
                                        <input type="text" class="form-control" id="signature_name" name="signature_name" 
                                               value="<?= old('signature_name', $org_data['signature_name'] ?? '') ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="signature_position" class="form-label">Signatory Position</label>
                                        <input type="text" class="form-control" id="signature_position" name="signature_position" 
                                               value="<?= old('signature_position', $org_data['signature_position'] ?? '') ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Official Stamp -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Official Stamp</h6>
                                    <div class="preview-container">
                                        <?php if (!empty($org_data['stamp_filepath'])): ?>
                                            <img src="<?= base_url(str_replace('public/', '', $org_data['stamp_filepath'])) ?>" 
                                                 alt="Organization Stamp" 
                                                 class="img-fluid mb-3" 
                                                 style="max-height: 120px;">
                                        <?php else: ?>
                                            <div class="alert alert-info">No stamp image uploaded</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-3">
                                        <label for="stamp_filepath" class="form-label">Upload Official Stamp</label>
                                        <input type="file" class="form-control" id="stamp_filepath" 
                                               name="stamp_filepath" accept="image/*" onchange="previewImage(this, 'stampPreview')">
                                        <small class="text-muted">Upload stamp image (PNG or JPG, max 10MB)</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Stamp -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Approval Stamp</h6>
                                    <div class="preview-container">
                                        <?php if (!empty($org_data['approved_stamp_filepath'])): ?>
                                            <img src="<?= base_url(str_replace('public/', '', $org_data['approved_stamp_filepath'])) ?>" 
                                                 alt="Approval Stamp" 
                                                 class="img-fluid mb-3" 
                                                 style="max-height: 120px;">
                                        <?php else: ?>
                                            <div class="alert alert-info">No approval stamp image uploaded</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-3">
                                        <label for="approved_stamp_filepath" class="form-label">Upload Approval Stamp</label>
                                        <input type="file" class="form-control" id="approved_stamp_filepath" 
                                               name="approved_stamp_filepath" accept="image/*" onchange="previewImage(this, 'approvalStampPreview')">
                                        <small class="text-muted">Upload approval stamp image (PNG or JPG, max 10MB)</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">Settings</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="is_active" 
                                       name="is_active" value="1" 
                                       <?= (old('is_active', $org_data['is_active'] ?? 1) == 1) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">
                                    Organization Active
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="license_status" class="form-label">License Status *</label>
                                <select class="form-select" id="license_status" name="license_status" required>
                                    <option value="">Select Status</option>
                                    <option value="active" <?= (old('license_status', $org_data['license_status'] ?? '') == 'active') ? 'selected' : '' ?>>Active</option>
                                    <option value="expired" <?= (old('license_status', $org_data['license_status'] ?? '') == 'expired') ? 'selected' : '' ?>>Expired</option>
                                    <option value="pending" <?= (old('license_status', $org_data['license_status'] ?? '') == 'pending') ? 'selected' : '' ?>>Pending</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                        <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add this script at the end of the file -->
<script>
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            if (preview.tagName === 'DIV') {
                // Replace the div with an img element
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'image-preview';
                img.id = previewId;
                preview.parentNode.replaceChild(img, preview);
            } else {
                preview.src = e.target.result;
            }
        }
        reader.readAsDataURL(file);
    }
}

function removeSignature() {
    document.getElementById('signature_filepath').value = '';
    const preview = document.getElementById('signaturePreview');
    if (preview.tagName === 'IMG') {
        const div = document.createElement('div');
        div.className = 'no-image';
        div.id = 'signaturePreview';
        div.innerHTML = '<span><i class="fas fa-signature fa-2x"></i></span>';
        preview.parentNode.replaceChild(div, preview);
    }
}

function removeStamp() {
    document.getElementById('stamp_filepath').value = '';
    const preview = document.getElementById('stampPreview');
    if (preview.tagName === 'IMG') {
        const div = document.createElement('div');
        div.className = 'no-image';
        div.id = 'stampPreview';
        div.innerHTML = '<span><i class="fas fa-stamp fa-2x"></i></span>';
        preview.parentNode.replaceChild(div, preview);
    }
}

function removeApprovalStamp() {
    document.getElementById('approved_stamp_filepath').value = '';
    const preview = document.getElementById('approvalStampPreview');
    if (preview.tagName === 'IMG') {
        const div = document.createElement('div');
        div.className = 'no-image';
        div.id = 'approvalStampPreview';
        div.innerHTML = '<span><i class="fas fa-stamp fa-2x"></i></span>';
        preview.parentNode.replaceChild(div, preview);
    }
}
</script>

<?= $this->endSection() ?> 