<?php
/**
 * View file for managing pre-screening criteria for exercises
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
    
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-filter me-2"></i>Pre-Screening Criteria</h2>
            <p class="text-muted">Define and manage pre-screening criteria for job exercises</p>
        </div>
        <div class="col-md-6 text-end">
            <button id="backToExercises" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Exercise Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p><strong>Exercise Name:</strong> <span id="exerciseName"></span></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>Advertisement No:</strong> <span id="advertisementNo"></span></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>Status:</strong> <span id="exerciseStatus" class="badge"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Pre-Screening Criteria</h5>
                    <button id="addCriteriaBtn" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i> Add Criteria
                    </button>
                </div>
                <div class="card-body">
                    <div id="criteriaSections">
                        <!-- Criteria sections will be populated here -->
                        <div id="noCriteriaMessage" class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No pre-screening criteria defined yet. Add criteria to help filter applications.
                        </div>
                    </div>
                    
                    <div class="text-end mt-3">
                        <button id="saveCriteriaBtn" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> Save Criteria
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Criteria Modal -->
<div class="modal fade" id="criteriaModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="criteriaModalTitle">Add Pre-Screening Criteria</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="criteriaForm">
                    <div class="mb-3">
                        <label for="criteriaName" class="form-label">Criteria Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="criteriaName" name="criteriaName" 
                               placeholder="e.g., Education Requirements, Experience, Documents" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="criteriaDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="criteriaDescription" name="criteriaDescription" 
                                  rows="3" placeholder="Describe the criteria in detail"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCriteriaItemBtn">Save Criteria</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Constants
    const exerciseId = <?= $exercise_id ?? 'null' ?>;
    
    // Check if exercise ID is available
    if (!exerciseId) {
        toastr.error('No exercise specified. Please select an exercise first.');
        setTimeout(() => {
            window.location.href = '<?= base_url('exercises') ?>';
        }, 2000);
        return;
    }
    
    // Variables
    let criteriaList = [];
    let editingIndex = -1;
    
    // Initialize
    loadExerciseData();
    
    // Event handlers
    $('#backToExercises').click(function() {
        window.location.href = '<?= base_url('exercises') ?>';
    });
    
    $('#addCriteriaBtn').click(function() {
        editingIndex = -1;
        resetCriteriaForm();
        $('#criteriaModalTitle').text('Add Pre-Screening Criteria');
        $('#criteriaModal').modal('show');
    });
    
    $('#saveCriteriaItemBtn').click(function() {
        // Validate form
        if (!$('#criteriaForm')[0].checkValidity()) {
            $('#criteriaForm')[0].reportValidity();
            return;
        }
        
        // Get form data
        const criteriaItem = {
            name: $('#criteriaName').val(),
            description: $('#criteriaDescription').val() || ''
        };
        
        // Add to list or update existing
        if (editingIndex === -1) {
            criteriaList.push(criteriaItem);
        } else {
            criteriaList[editingIndex] = criteriaItem;
        }
        
        // Refresh UI
        renderCriteriaList();
        $('#criteriaModal').modal('hide');
    });
    
    $('#saveCriteriaBtn').click(function() {
        saveCriteria();
    });
    
    // Functions
    function loadExerciseData() {
        // Show loading state
        $('#exerciseName, #advertisementNo, #exerciseStatus').html('<span class="placeholder col-4"></span>');
        
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';
        
        // Create data object with CSRF token
        const data = {};
        data[csrfName] = csrfToken;
        
        // Fetch exercise data
        $.ajax({
            url: `<?= base_url('exercises/get') ?>/${exerciseId}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response) {
                    // Update exercise info
                    $('#exerciseName').text(response.exercise_name);
                    $('#advertisementNo').text(response.advertisement_no);
                    
                    // Set status with appropriate color
                    const statusText = response.status.charAt(0).toUpperCase() + response.status.slice(1).replace('_', ' ');
                    let statusClass = 'bg-secondary';
                    
                    if (response.status === 'draft') statusClass = 'bg-secondary';
                    else if (response.status === 'publish') statusClass = 'bg-success';
                    else if (response.status === 'selection') statusClass = 'bg-primary';
                    else if (response.status === 'closed') statusClass = 'bg-danger';
                    
                    $('#exerciseStatus').text(statusText).removeClass().addClass(`badge ${statusClass}`);
                    
                    // Load criteria if available
                    if (response.pre_screen_criteria) {
                        try {
                            criteriaList = JSON.parse(response.pre_screen_criteria);
                            renderCriteriaList();
                        } catch (e) {
                            console.error('Error parsing criteria JSON:', e);
                            criteriaList = [];
                        }
                    }
                } else {
                    toastr.error('Exercise not found');
                }
            },
            error: function() {
                toastr.error('Failed to load exercise data');
            }
        });
    }
    
    function renderCriteriaList() {
        const container = $('#criteriaSections');
        container.empty();
        
        if (criteriaList.length === 0) {
            container.append(`
                <div id="noCriteriaMessage" class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No pre-screening criteria defined yet. Add criteria to help filter applications.
                </div>
            `);
            return;
        }
        
        criteriaList.forEach((criteria, index) => {
            container.append(`
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${criteria.name}</strong>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary edit-criteria-btn" data-index="${index}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-criteria-btn" data-index="${index}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        ${criteria.description ? `<p>${criteria.description}</p>` : '<p class="text-muted">No description provided</p>'}
                    </div>
                </div>
            `);
        });
        
        // Re-attach event handlers
        $('.edit-criteria-btn').click(function() {
            const index = $(this).data('index');
            editCriteria(index);
        });
        
        $('.delete-criteria-btn').click(function() {
            const index = $(this).data('index');
            if (confirm('Are you sure you want to delete this criteria?')) {
                criteriaList.splice(index, 1);
                renderCriteriaList();
            }
        });
    }
    
    function editCriteria(index) {
        editingIndex = index;
        const criteria = criteriaList[index];
        
        // Fill form with criteria data
        $('#criteriaName').val(criteria.name);
        $('#criteriaDescription').val(criteria.description || '');
        
        // Update modal title and show
        $('#criteriaModalTitle').text('Edit Pre-Screening Criteria');
        $('#criteriaModal').modal('show');
    }
    
    function resetCriteriaForm() {
        $('#criteriaForm')[0].reset();
    }
    
    function saveCriteria() {
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';
        
        // Create data object
        const data = {
            pre_screen_criteria: JSON.stringify(criteriaList)
        };
        data[csrfName] = csrfToken;
        
        // Disable save button
        $('#saveCriteriaBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');
        
        // Save criteria to database
        $.ajax({
            url: `<?= base_url('exercises/save_criteria') ?>/${exerciseId}`,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                toastr.error('An error occurred while saving criteria');
                
                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }
            },
            complete: function() {
                // Re-enable save button
                $('#saveCriteriaBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Criteria');
            }
        });
    }
});
</script>
<?= $this->endSection() ?> 