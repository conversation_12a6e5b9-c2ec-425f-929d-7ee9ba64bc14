<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-landmark"></i> LLGs of <?= esc($district['name']) ?>
                        </h5>
                        <small class="text-white-50">
                            Province: <?= esc($province['name']) ?> |
                            District Code: <?= esc($district['districtcode']) ?>
                        </small>
                    </div>
                    <div>
                        <a href="<?= base_url('dakoii/district/list/' . $province['id']) ?>" class="btn btn-light mr-2">
                            <i class="fas fa-arrow-left"></i> Back to Districts
                        </a>
                        <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addLLGModal">
                            <i class="fas fa-plus"></i> Add LLG
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>#</th>
                                <th>LLG Code</th>
                                <th>JSON ID</th>
                                <th>Name</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($llgs as $llg): ?>
                                <tr>
                                    <td><?= $i++ ?></td>
                                    <td><?= esc($llg['llgcode']) ?></td>
                                    <td><?= esc($llg['json_id']) ?></td>
                                    <td><?= esc($llg['name']) ?></td>
                                    <td>
                                        <a href="<?= base_url('dakoii/ward/list/' . $llg['id']) ?>"
                                            class="btn btn-sm btn-info"
                                            title="Manage Wards">
                                            <i class="fas fa-sitemap"></i>
                                        </a>
                                        <button class="btn btn-sm btn-primary edit-llg"
                                            data-id="<?= $llg['id'] ?>"
                                            data-name="<?= esc($llg['name']) ?>"
                                            data-code="<?= esc($llg['llgcode']) ?>"
                                            data-toggle="modal"
                                            data-target="#editLLGModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="<?= base_url('dakoii/llg/delete/' . $llg['id']) ?>"
                                            class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure you want to delete the LLG <?= esc($llg['name']) ?> from <?= esc($district['name']) ?> district? This action cannot be undone.')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">LLG JSON Data</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="llgJsonTable" class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>FID</th>
                                    <th>GEOCODE</th>
                                    <th>LLG Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $json_file = file_get_contents(base_url(JSON_MAP_LLG));
                                $data = json_decode($json_file, true);

                                foreach ($data['features'] as $feature) {
                                    $properties = $feature['properties'];
                                ?>
                                    <tr>
                                        <td><?= esc($properties['FID']) ?></td>
                                        <td><?= esc($properties['GEOCODE']) ?></td>
                                        <td><?= esc($properties['LLGNAME']) ?></td>
                                    </tr>
                                <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                    <script>
                        $(document).ready(function() {
                            $('#llgJsonTable').DataTable({
                                "pageLength": 10,
                                "ordering": true,
                                "searching": true,
                                "responsive": true
                            });
                        });
                    </script>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add LLG Modal -->
<div class="modal fade" id="addLLGModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">Add New LLG</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dakoii/llg/create') ?>
            <div class="modal-body">
                <input type="hidden" name="country_id" value="<?= $province['country_id'] ?>">
                <input type="hidden" name="province_id" value="<?= $province['id'] ?>">
                <input type="hidden" name="district_id" value="<?= $district['id'] ?>">

                <div class="form-group">
                    <label>LLG Code</label>
                    <input type="text" class="form-control" name="llgcode" required>
                </div>

                <div class="form-group">
                    <label>LLG Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>

                <div class="form-group">
                    <label>JSON ID</label>
                    <input type="text" class="form-control" name="json_id" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save LLG</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit LLG Modal -->
<div class="modal fade" id="editLLGModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Edit LLG</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dakoii/llg/update') ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="district_id" value="<?= $district['id'] ?>">

                <div class="form-group">
                    <label>LLG Code</label>
                    <input type="text" class="form-control" name="llgcode" id="edit_llgcode" required>
                </div>

                <div class="form-group">
                    <label>LLG Name</label>
                    <input type="text" class="form-control" name="name" id="edit_name" required>
                </div>

                <div class="form-group">
                    <label>JSON ID</label>
                    <input type="text" class="form-control" name="json_id" id="edit_json_id" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update LLG</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle edit LLG button clicks
        document.querySelectorAll('.edit-llg').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;
                const name = this.dataset.name;
                const code = this.dataset.code;
                const json_id = this.dataset.json_id;
                document.getElementById('edit_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_llgcode').value = code;
                document.getElementById('edit_json_id').value = json_id;
            });
        });
    });
</script>

<?= $this->endSection() ?>