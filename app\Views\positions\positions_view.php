<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section with Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Manage Positions</h1>
            <p class="mb-0 text-muted">View and manage positions in this group</p>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('positions/positions_exercises') ?>">Exercises</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('positions/positions_groups/' . $group['exercise_id']) ?>">Position Groups</a></li>
                <li class="breadcrumb-item active" aria-current="page">Positions</li>
            </ol>
        </nav>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-briefcase me-1"></i>
                Positions List
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
                <i class="fas fa-plus"></i> Add Position
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="positionsTable">
                    <thead>
                        <tr>
                            <th>Reference</th>
                            <th>Designation</th>
                            <th>Classification</th>
                            <th>Award</th>
                            <th>Location</th>
                            <th>Annual Salary</th>
                            <th>Status</th>
                            <th>JD</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($positions as $position): ?>
                        <tr>
                            <td><?= esc($position['position_reference']) ?></td>
                            <td><?= esc($position['designation']) ?></td>
                            <td><?= esc($position['classification']) ?></td>
                            <td><?= esc($position['award']) ?></td>
                            <td><?= esc($position['location']) ?></td>
                            <td><?= esc($position['annual_salary']) ?></td>
                            <td><?= esc($position['status']) ?></td>
                            <td>
                                <?php if (!empty($position['jd_filepath'])): ?>
                                    <a href="<?= base_url($position['jd_filepath']) ?>" class="btn btn-sm btn-primary" target="_blank" title="Download Job Description">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="badge bg-secondary">No JD</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal<?= $position['id'] ?>">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal<?= $position['id'] ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $position['id'] ?>">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- View Modal for each position -->
                        <div class="modal fade" id="viewModal<?= $position['id'] ?>" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header bg-info text-white">
                                        <h5 class="modal-title">
                                            <i class="fas fa-eye me-2"></i>
                                            Position Details
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <!-- Basic Information -->
                                            <div class="col-md-12 mb-4">
                                                <h6 class="border-bottom pb-2 text-primary">Basic Information</h6>
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Position Reference:</label>
                                                        <p><?= esc($position['position_reference']) ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Designation:</label>
                                                        <p><?= esc($position['designation']) ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Classification:</label>
                                                        <p><?= esc($position['classification']) ?: 'N/A' ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Status:</label>
                                                        <p>
                                                            <span class="badge bg-<?= $position['status'] == 'active' ? 'success' : 'danger' ?>">
                                                                <?= ucfirst($position['status']) ?>
                                                            </span>
                                                        </p>
                                                    </div>
                                                    <?php if (!empty($position['jd_filepath'])): ?>
                                                    <div class="col-12">
                                                        <label class="fw-bold">Job Description Document:</label>
                                                        <p>
                                                            <a href="<?= base_url($position['jd_filepath']) ?>" class="btn btn-sm btn-primary" target="_blank">
                                                                <i class="fas fa-download me-1"></i> Download JD
                                                            </a>
                                                        </p>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <!-- Compensation -->
                                            <div class="col-md-12 mb-4">
                                                <h6 class="border-bottom pb-2 text-primary">Compensation</h6>
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Award:</label>
                                                        <p><?= esc($position['award']) ?: 'N/A' ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Location:</label>
                                                        <p><?= esc($position['location']) ?: 'N/A' ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="fw-bold">Annual Salary:</label>
                                                        <p><?= esc($position['annual_salary']) ?: 'N/A' ?></p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Requirements -->
                                            <div class="col-md-12 mb-4">
                                                <h6 class="border-bottom pb-2 text-primary">Requirements & Qualifications</h6>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Qualifications:</label>
                                                    <p class="text-justify"><?= nl2br(esc($position['qualifications'])) ?: 'N/A' ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Knowledge Required:</label>
                                                    <p class="text-justify"><?= nl2br(esc($position['knowledge'])) ?: 'N/A' ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Skills & Competencies:</label>
                                                    <p class="text-justify"><?= nl2br(esc($position['skills_competencies'])) ?: 'N/A' ?></p>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Job Experience Requirements:</label>
                                                    <p class="text-justify"><?= nl2br(esc($position['job_experiences'])) ?: 'N/A' ?></p>
                                                </div>
                                            </div>

                                            <!-- Additional Information -->
                                            <div class="col-md-12">
                                                <h6 class="border-bottom pb-2 text-primary">Additional Information</h6>
                                                <div class="mb-3">
                                                    <label class="fw-bold">Remarks:</label>
                                                    <p class="text-justify"><?= nl2br(esc($position['remarks'])) ?: 'N/A' ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Edit Modal for each position -->
                        <div class="modal fade" id="editModal<?= $position['id'] ?>" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Edit Position</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <form id="editPositionForm<?= $position['id'] ?>" onsubmit="return updatePosition(event, <?= $position['id'] ?>)" enctype="multipart/form-data">
                                        <?= csrf_field() ?>
                                        <input type="hidden" name="id" value="<?= $position['id'] ?>">
                                        <div class="modal-body">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Position Reference</label>
                                                    <input type="text" class="form-control" name="position_reference" value="<?= esc($position['position_reference']) ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Designation</label>
                                                    <input type="text" class="form-control" name="designation" value="<?= esc($position['designation']) ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Classification</label>
                                                    <input type="text" class="form-control" name="classification" value="<?= esc($position['classification']) ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Award</label>
                                                    <input type="text" class="form-control" name="award" value="<?= esc($position['award']) ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Location</label>
                                                    <input type="text" class="form-control" name="location" value="<?= esc($position['location']) ?>" placeholder="e.g., Port Moresby, Lae">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Annual Salary</label>
                                                    <input type="text" class="form-control" name="annual_salary" value="<?= esc($position['annual_salary']) ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Status</label>
                                                    <select class="form-select" name="status" required>
                                                        <option value="active" <?= $position['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                                                        <option value="inactive" <?= $position['status'] == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                                    </select>
                                                </div>
                                                <div class="col-12">
                                                    <label class="form-label">Qualifications</label>
                                                    <textarea class="form-control" name="qualifications" rows="3"><?= esc($position['qualifications']) ?></textarea>
                                                </div>
                                                <div class="col-12">
                                                    <label class="form-label">Knowledge</label>
                                                    <textarea class="form-control" name="knowledge" rows="3"><?= esc($position['knowledge']) ?></textarea>
                                                </div>
                                                <div class="col-12">
                                                    <label class="form-label">Skills & Competencies</label>
                                                    <textarea class="form-control" name="skills_competencies" rows="3"><?= esc($position['skills_competencies']) ?></textarea>
                                                </div>
                                                <div class="col-12">
                                                    <label class="form-label">Job Experiences</label>
                                                    <textarea class="form-control" name="job_experiences" rows="3"><?= esc($position['job_experiences']) ?></textarea>
                                                </div>
                                                <div class="col-12">
                                                    <label class="form-label">Remarks</label>
                                                    <textarea class="form-control" name="remarks" rows="2"><?= esc($position['remarks']) ?></textarea>
                                                </div>
                                                <div class="col-12">
                                                    <label class="form-label">Job Description File (PDF only)</label>
                                                    <input type="file" class="form-control" name="jd_file" accept=".pdf">
                                                    <?php if (!empty($position['jd_filepath'])): ?>
                                                    <small class="text-muted">Current file: <a href="<?= base_url('positions/downloadJD/' . $position['id']) ?>">Download</a></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            <button type="submit" class="btn btn-primary">Update Position</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Delete Modal for each position -->
                        <div class="modal fade" id="deleteModal<?= $position['id'] ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Delete Position</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p>Are you sure you want to delete this position?</p>
                                        <p><strong>Position:</strong><?= esc($position['position_reference']) ?> - <?= esc($position['designation']) ?></p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-danger" onclick="deletePosition(<?= $position['id'] ?>)">Delete Position</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Position</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPositionForm" onsubmit="return addPosition(event)" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <input type="hidden" name="position_group_id" value="<?= $group_id ?>">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Position Reference</label>
                            <input type="text" class="form-control" name="position_reference" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Designation</label>
                            <input type="text" class="form-control" name="designation" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Classification</label>
                            <input type="text" class="form-control" name="classification">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Award</label>
                            <input type="text" class="form-control" name="award">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control" name="location" placeholder="e.g., Port Moresby, Lae">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Annual Salary</label>
                            <input type="text" class="form-control" name="annual_salary">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Qualifications</label>
                            <textarea class="form-control" name="qualifications" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Knowledge</label>
                            <textarea class="form-control" name="knowledge" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Skills & Competencies</label>
                            <textarea class="form-control" name="skills_competencies" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Job Experiences</label>
                            <textarea class="form-control" name="job_experiences" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Remarks</label>
                            <textarea class="form-control" name="remarks" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Job Description File (PDF only)</label>
                            <input type="file" class="form-control" name="jd_file" accept=".pdf">
                            <small class="text-muted">Upload PDF file only</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Position</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable();
});

// Add Position Function
function addPosition(event) {
    event.preventDefault();
    
    let formData = new FormData($('#addPositionForm')[0]);
    
    $.ajax({
        url: '<?= base_url('positions/add') ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#addPositionModal').modal('hide');
            if (response.success) {
                toastr.success(response.message);
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                toastr.error(response.message || 'Failed to add position');
            }
        },
        error: function(xhr) {
            $('#addPositionModal').modal('hide');
            if (xhr.status === 403) {
                toastr.error('Session expired. Please refresh the page.');
            } else {
                toastr.error('An error occurred while adding the position');
            }
        }
    });
    
    return false;
}

// Update Position Function
function updatePosition(event, id) {
    event.preventDefault();
    
    let formData = new FormData($('#editPositionForm' + id)[0]);
    
    $.ajax({
        url: '<?= base_url('positions/update') ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#editModal' + id).modal('hide');
            if (response.success) {
                toastr.success(response.message);
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                // Handle validation errors
                if (response.errors) {
                    let errorMessage = 'Validation Errors:<br>';
                    Object.keys(response.errors).forEach(function(key) {
                        errorMessage += `- ${response.errors[key]}<br>`;
                    });
                    toastr.error(errorMessage);
                } else {
                    toastr.error(response.message || 'Failed to update position');
                }
                $('#editModal' + id).modal('show');
            }
        },
        error: function(xhr, status, error) {
            $('#editModal' + id).modal('hide');
            if (xhr.status === 403) {
                toastr.error('Session expired. Please refresh the page.');
            } else {
                toastr.error('An error occurred while updating the position: ' + error);
                console.error('Update error:', xhr.responseText);
            }
        }
    });
    
    return false;
}

// Delete Position Function
function deletePosition(id) {
    $.ajax({
        url: '<?= base_url('positions/delete/') ?>' + id,
        type: 'POST',
        data: {
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        },
        success: function(response) {
            $('#deleteModal' + id).modal('hide');
            if (response.success) {
                toastr.success(response.message);
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                toastr.error(response.message || 'Failed to delete position');
            }
        },
        error: function(xhr) {
            $('#deleteModal' + id).modal('hide');
            if (xhr.status === 403) {
                toastr.error('Session expired. Please refresh the page.');
            } else {
                toastr.error('An error occurred while deleting the position');
            }
        }
    });
}
</script>
<?= $this->endSection() ?> 