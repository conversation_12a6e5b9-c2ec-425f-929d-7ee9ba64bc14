<?= $this->extend('templates/home_template') ?>

<?= $this->section('css') ?>
<style>
    /* Page-specific styles for home page */
    .job-card {
      position: relative;
      overflow: hidden;
    }

    .job-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, var(--yellow), var(--yellow-dark));
      border-radius: 4px 0 0 4px;
    }

    .stat-card {
      background-color: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.08);
      transform: translateY(0);
      transition: all 0.3s ease;
      border: none;
    }

    .stat-card:hover {
      transform: translateY(-0.35rem);
      box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.1);
    }

    .position-card {
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .position-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
      border-top: 3px solid var(--red);
    }

    .position-card .card-body {
      padding: 1.5rem;
    }

    .position-meta {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .position-deadline {
      font-size: 0.875rem;
      color: var(--red);
      font-weight: 500;
    }

</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

  <!-- Hero Section -->
  <section id="home" class="gradient-bg text-white pt-5 pb-5" style="margin-top: 76px;">
    <div class="content-wrapper container text-center">
      <h1 class="display-4 fw-bold mb-4">Find Your Next Government Position</h1>
      <p class="fs-4 mb-3 text-white-75">Explore opportunities across various government departments</p>
      <p class="fs-5 mb-5 text-white-75">Your gateway to a rewarding career in public service</p>
    </div>
  </section>

  <!-- Featured Positions Section -->
  <section class="py-5 bg-white">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 fw-bold text-navy mb-0">Featured Government Positions</h2>
        <a href="<?= base_url('jobs') ?>" class="btn btn-outline-danger">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-briefcase me-2" viewBox="0 0 16 16">
            <path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v8A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-8A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5m1.886 6.914L15 7.151V12.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V7.15l6.614 1.764a1.5 1.5 0 0 0 .772 0M1.5 4h13a.5.5 0 0 1 .5.5v1.616L8.129 7.948a.5.5 0 0 1-.258 0L1 6.116V4.5a.5.5 0 0 1 .5-.5"/>
          </svg>
          View All Positions
        </a>
      </div>

      <?php if (empty($latest_positions)): ?>
        <div class="card">
          <div class="card-body text-center py-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" class="bi bi-clipboard-x text-muted mb-3" viewBox="0 0 16 16">
              <path d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5z"/>
              <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5zm4 7.793 1.146-1.147a.5.5 0 1 1 .708.708L8.707 10l1.147 1.146a.5.5 0 0 1-.708.708L8 10.707l-1.146 1.147a.5.5 0 0 1-.708-.708L7.293 10 6.146 8.854a.5.5 0 1 1 .708-.708z"/>
            </svg>
            <h3 class="h4 text-muted mb-2">No Positions Available</h3>
            <p class="text-muted mb-4">Check back later for new government positions.</p>
            <a href="<?= base_url('jobs') ?>" class="btn btn-red">
              Browse All Positions
            </a>
          </div>
        </div>
      <?php else: ?>
        <div class="row g-4">
          <?php foreach ($latest_positions as $position): ?>
            <div class="col-md-6 col-lg-4">
              <div class="position-card card h-100">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h3 class="h5 fw-bold text-red mb-0"><?= esc($position['designation'] ?? 'Position Not Specified') ?></h3>
                    <span class="badge bg-yellow"><?= esc($position['classification'] ?? 'Not Specified') ?></span>
                  </div>
                  <div class="position-meta mb-2">
                    <div class="mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-building me-2" viewBox="0 0 16 16">
                        <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v10.5a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5V2.5zm1 0v10h6V2.5H5z"/>
                        <path d="M2 2a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2zm13 2v11H1V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1z"/>
                      </svg>
                      <?= esc($position['org_name'] ?? 'Organization Not Specified') ?>
                    </div>
                    <div class="mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-2" viewBox="0 0 16 16">
                        <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                        <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                      </svg>
                      <?= esc($position['location'] ?? 'Location Not Specified') ?>
                    </div>
                    <div>
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cash me-2" viewBox="0 0 16 16">
                        <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                        <path d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z"/>
                      </svg>
                      <?= esc($position['annual_salary'] ?? 'Salary Not Specified') ?>
                    </div>
                  </div>
                  <div class="position-deadline mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar-event me-2" viewBox="0 0 16 16">
                      <path d="M11 6.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                      <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                    </svg>
                    Closes on <?= isset($position['publish_date_to']) ? date('d M Y', strtotime($position['publish_date_to'])) : 'Date Not Specified' ?>
                  </div>
                  <a href="<?= base_url('jobs/view/' . ($position['id'] ?? '1')) ?>" class="btn btn-red w-100">View Details</a>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </div>
  </section>

  <!-- Statistics Section -->
  <section class="py-5 bg-light">
    <div class="container">
      <div class="row g-4">
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-red mb-2">2,500+</div>
            <div class="text-muted">Government Positions</div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-yellow mb-2">100+</div>
            <div class="text-muted">Government Agencies</div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-gray mb-2">5,000+</div>
            <div class="text-muted">Active Applicants</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-5 bg-light">
    <div class="container">
      <h2 class="display-5 fw-bold text-center mb-3">System Features</h2>
      <p class="text-center text-muted mb-5 mx-auto" style="max-width: 700px;">Discover the powerful tools and features designed to make your public service experience seamless.</p>

      <div class="row g-4">
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4">
              <h3 class="h5 fw-bold mb-3 text-navy">Easy Application Process</h3>
              <p class="text-muted">Submit your applications with just a few clicks. Our streamlined process makes job hunting effortless.</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4">
              <h3 class="h5 fw-bold mb-3 text-navy">Real-time Updates</h3>
              <p class="text-muted">Stay informed with instant notifications about your application status and new job postings.</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4">
              <h3 class="h5 fw-bold mb-3 text-navy">Document Management</h3>
              <p class="text-muted">Securely store and manage your documents. Upload once, use anywhere within the system.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Apply Section -->
  <section id="apply" class="py-5 bg-light">
    <div class="container">
      <h2 class="display-5 fw-bold text-center mb-3 text-navy">Applicant Portal</h2>
      <p class="text-center text-muted mb-5">Login to apply for positions or create a new account</p>

      <?php if (session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <?= session()->getFlashdata('success') ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <?php if (session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <?= session()->getFlashdata('error') ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <?php if (session()->has('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
          <?= session()->getFlashdata('warning') ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <?php if (session()->has('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
              <li><?= $error ?></li>
            <?php endforeach; ?>
          </ul>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      <?php endif; ?>

      <div class="row g-4 justify-content-center" style="max-width: 1000px; margin: 0 auto;">
        <!-- Login Form -->
        <div class="col-md-6">
          <div class="card h-100 border-top border-4 border-red">
            <div class="card-body p-4">
              <h3 class="h4 fw-bold mb-4 text-red">Login</h3>
              <form action="<?= base_url('applicant/login') ?>" method="post">
                <?= csrf_field() ?>
                <div class="mb-3">
                  <input type="email" name="email" class="form-control" placeholder="Email address" required value="<?= old('email') ?>">
                </div>
                <div class="mb-4">
                  <input type="password" name="password" class="form-control" placeholder="Password" required>
                </div>
                <button type="submit" class="btn btn-red w-100">Login</button>
              </form>
            </div>
          </div>
        </div>

        <!-- Register Form -->
        <div class="col-md-6">
          <div class="card h-100 border-top border-4 border-yellow">
            <div class="card-body p-4">
              <h3 class="h4 fw-bold mb-4 text-yellow">Create Account</h3>
              <form action="<?= base_url('applicant/register') ?>" method="post">
                <?= csrf_field() ?>
                <div class="row g-3 mb-3">
                  <div class="col-md-6">
                    <input type="text" name="firstname" class="form-control <?= session()->has('errors.firstname') ? 'is-invalid' : '' ?>" placeholder="First name" required value="<?= old('firstname') ?>">
                    <?php if (session()->has('errors.firstname')): ?>
                      <div class="invalid-feedback"><?= session()->getFlashdata('errors')['firstname'] ?></div>
                    <?php endif; ?>
                  </div>
                  <div class="col-md-6">
                    <input type="text" name="lastname" class="form-control <?= session()->has('errors.lastname') ? 'is-invalid' : '' ?>" placeholder="Last name" required value="<?= old('lastname') ?>">
                    <?php if (session()->has('errors.lastname')): ?>
                      <div class="invalid-feedback"><?= session()->getFlashdata('errors')['lastname'] ?></div>
                    <?php endif; ?>
                  </div>
                </div>
                <div class="mb-3">
                  <input type="email" name="email" class="form-control <?= session()->has('errors.email') ? 'is-invalid' : '' ?>" placeholder="Email address" required value="<?= old('email') ?>">
                  <?php if (session()->has('errors.email')): ?>
                    <div class="invalid-feedback"><?= session()->getFlashdata('errors')['email'] ?></div>
                  <?php endif; ?>
                </div>
                <div class="mb-3">
                  <input type="password" name="password" class="form-control <?= session()->has('errors.password') ? 'is-invalid' : '' ?>" placeholder="Password" required>
                  <?php if (session()->has('errors.password')): ?>
                    <div class="invalid-feedback"><?= session()->getFlashdata('errors')['password'] ?></div>
                  <?php endif; ?>
                </div>
                <div class="mb-4">
                  <input type="password" name="confirm_password" class="form-control <?= session()->has('errors.confirm_password') ? 'is-invalid' : '' ?>" placeholder="Confirm password" required>
                  <?php if (session()->has('errors.confirm_password')): ?>
                    <div class="invalid-feedback"><?= session()->getFlashdata('errors')['confirm_password'] ?></div>
                  <?php endif; ?>
                </div>
                <button type="submit" class="btn btn-yellow w-100">Create Account</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Admin Login Section -->
  <section id="login" class="py-5 gradient-bg">
    <div class="content-wrapper container text-center">
      <h2 class="display-5 fw-bold mb-4 text-white">Admin Portal</h2>
      <p class="fs-5 mb-5 text-white">Access the administrative dashboard</p>

      <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
          <div class="card">
            <div class="card-body p-4">
              <h3 class="h4 fw-bold mb-4 text-red">Admin Login</h3>
              <form action="<?= base_url('login') ?>" method="post">
                <?= csrf_field() ?>
                <div class="mb-3">
                  <input type="text" name="username" class="form-control" placeholder="Username" required>
                </div>
                <div class="mb-4">
                  <input type="password" name="password" class="form-control" placeholder="Password" required>
                </div>
                <button type="submit" class="btn btn-red w-100">Login</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

<?= $this->endSection() ?>
