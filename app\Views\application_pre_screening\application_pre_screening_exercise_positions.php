<?php
/**
 * View file for listing positions with applications for pre-screening
 * 
 * @var array $exercise Exercise details
 * @var array $positions Positions with application counts
 * @var array $positionsLookup Lookup array for position details
 * @var array $positionGroups Position groups for this exercise
 * @var array $positionGroupsLookup Lookup array for position group details
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-briefcase me-2"></i>Positions for Pre-Screening</h2>
            <p class="text-muted">Exercise: <?= esc($exercise['exercise_name']) ?></p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('applications_pre_screening_exercise') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Exercise Details</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                    <p><strong>Gazzetted No:</strong> <?= esc($exercise['gazzetted_no']) ?></p>
                </div>
                <div class="col-md-4">
                    <p><strong>Advertisement Date:</strong> <?= date('d M Y', strtotime($exercise['advertisement_date'])) ?></p>
                    <p><strong>Gazzetted Date:</strong> <?= date('d M Y', strtotime($exercise['gazzetted_date'])) ?></p>
                </div>
                <div class="col-md-4">
                    <p><strong>Publish Date Range:</strong> <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> to <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?></p>
                    <p><strong>Mode of Advertisement:</strong> <?= esc($exercise['mode_of_advertisement']) ?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No positions with pending applications found for pre-screening in this exercise.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">Position Name</th>
                                <th width="20%">Position Group</th>
                                <th width="15%">Applications</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($position['position_name']) ?></td>
                                    <td><?= esc($position['position_group_name'] ?? 'None') ?></td>
                                    <td>
                                        <span class="badge bg-primary rounded-pill">
                                            <?= $position['application_count'] ?> applications
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('applications_pre_screening/position/' . $position['position_id']) ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-filter"></i> View Applications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add global AJAX error handler for 401 unauthorized responses (session timeout)
    $(document).ajaxError(function(event, jqXHR, settings, thrownError) {
        if (jqXHR.status === 401) {
            try {
                const response = JSON.parse(jqXHR.responseText);
                if (response.redirect) {
                    toastr.error(response.message || 'Your session has expired. Please login again.');
                    setTimeout(function() {
                        window.location.href = response.redirect;
                    }, 2000); // Redirect after 2 seconds
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                toastr.error('Your session has expired. Please login again.');
                setTimeout(function() {
                    window.location.href = '<?= base_url() ?>';
                }, 2000);
            }
        }
    });
    
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[3, 'desc']], // Sort by application count desc
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions available with applications for pre-screening",
        }
    });
});
</script>
<?= $this->endSection() ?> 