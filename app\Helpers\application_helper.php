<?php

if (!function_exists('get_status_color')) {
    /**
     * Get the appropriate Bootstrap color class for an application status
     *
     * @param string $status The application status
     * @return string The Bootstrap color class
     */
    function get_status_color(string $status): string
    {
        return match (strtolower($status)) {
            'pending' => 'warning',
            'shortlisted' => 'info',
            'interviewed' => 'primary',
            'selected' => 'success',
            'rejected' => 'danger',
            default => 'secondary',
        };
    }
}

if (!function_exists('time_elapsed_string')) {
    /**
     * Returns a string representing how long ago a date was
     *
     * @param string $datetime Date/time string
     * @param bool $full Whether to show the full string or just the first part
     * @return string
     */
    function time_elapsed_string(string $datetime, bool $full = false): string
    {
        $now = new \DateTime;
        $ago = new \DateTime($datetime);
        $diff = $now->diff($ago);

        $string = array(
            'y' => 'year',
            'm' => 'month',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        );

        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
            } else {
                unset($string[$k]);
            }
        }

        if (!$full) {
            $string = array_slice($string, 0, 1);
        }

        return $string ? implode(', ', $string) . ' ago' : 'just now';
    }
} 