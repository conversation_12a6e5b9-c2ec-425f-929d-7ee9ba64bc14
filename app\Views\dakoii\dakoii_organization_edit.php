<?= $this->extend('templates/dakoiitemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Organization: <?= esc($org['org_name']) ?></h3>
                    <div class="card-tools">
                        <a href="<?= base_url('dakoii/organization/view/' . $org['org_code']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to View
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?= form_open_multipart('dakoii/organization/update', ['class' => 'form-horizontal']) ?>
                    
                    <input type="hidden" name="id" value="<?= $org['id'] ?>">
                    <input type="hidden" name="orgcode" value="<?= $org['org_code'] ?>">

                    <div class="form-group row">
                        <label for="org_name" class="col-sm-2 col-form-label">Organization Name <span class="text-danger">*</span></label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="org_name" name="org_name" value="<?= esc($org['org_name']) ?>" required>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="description" class="col-sm-2 col-form-label">Description</label>
                        <div class="col-sm-10">
                            <textarea class="form-control" id="description" name="description" rows="3"><?= esc($org['description']) ?></textarea>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="country" class="col-sm-2 col-form-label">Country</label>
                        <div class="col-sm-10">
                            <select class="form-control" id="country" name="country">
                                <option value="">Select Country</option>
                                <option value="<?= $set_country['id'] ?>" <?= ($org['location_lock_country'] == $set_country['id']) ? 'selected' : '' ?>>
                                    <?= esc($set_country['name']) ?>
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="province" class="col-sm-2 col-form-label">Province</label>
                        <div class="col-sm-10">
                            <select class="form-control" id="province" name="province">
                                <option value="">Select Province</option>
                                <?php foreach ($get_provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>" <?= ($org['location_lock_province'] == $province['id']) ? 'selected' : '' ?>>
                                        <?= esc($province['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="status" class="col-sm-2 col-form-label">Status</label>
                        <div class="col-sm-10">
                            <select class="form-control" id="status" name="status">
                                <option value="1" <?= ($org['is_active'] == 1) ? 'selected' : '' ?>>Active</option>
                                <option value="0" <?= ($org['is_active'] == 0) ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="org_logo" class="col-sm-2 col-form-label">Organization Logo</label>
                        <div class="col-sm-10">
                            <input type="file" class="form-control-file" id="org_logo" name="org_logo" accept="image/*">
                            <small class="form-text text-muted">Upload new logo to replace current one (optional)</small>
                            <?php if (!empty($org['logo_path'])): ?>
                                <div class="mt-2">
                                    <img src="<?= base_url($org['logo_path']) ?>" alt="Current Logo" style="max-height: 100px;">
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-10 offset-sm-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Organization
                            </button>
                            <a href="<?= base_url('dakoii/organization/view/' . $org['org_code']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
