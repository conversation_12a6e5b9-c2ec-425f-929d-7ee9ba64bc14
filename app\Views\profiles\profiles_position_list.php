<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position_group['exercise_id'] ?? '#')) ?>"><?= esc($position_group['exercise_name'] ?? 'Exercise') ?></a></li> 
            <li class="breadcrumb-item active" aria-current="page"><?= esc($position_group['group_name'] ?? 'Position Group') ?></li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Positions') ?></h1>
            <a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position_group['exercise_id'] ?? '#')) ?>" class="btn btn-sm btn-secondary">Back to Position Groups</a>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <p class="mb-4">Select a position to view applicants who have passed pre-screening and are ready for profiling.</p>

            <?php if (!empty($positions) && is_array($positions)): ?>
                <div class="list-group">
                    <?php foreach ($positions as $position): ?>
                        <a href="<?= base_url('profile_applications_exercise/profile_position/' . esc($position['id'], 'url')) ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <span>
                                <i class="fas fa-briefcase me-2 text-info"></i>
                                <?= esc($position['designation'] ?? 'Unnamed Position') ?> 
                                <small class="text-muted">(<?= esc($position['position_reference'] ?? 'N/A') ?>)</small>
                                <div class="mt-1 small">
                                    <span class="badge bg-success rounded-pill"><?= esc($position['applicant_count'] ?? 0) ?> Applications</span>
                                </div>
                            </span>
                            <span class="badge bg-info rounded-pill">View Applicants <i class="fas fa-arrow-right ms-1"></i></span>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info" role="alert">
                    No positions found for this group.
                    <br><small>Note: Position data fetching is currently placeholder in the controller.</small>
                </div>
                 <!-- Placeholder for demonstration -->
                 <div class="list-group">
                    <a href="<?= base_url('profile_applications_exercise/profile_position/1') ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-briefcase me-2 text-info"></i>
                            Sample Position 1 (Placeholder)
                        </span>
                        <span class="badge bg-info rounded-pill">View Applicants <i class="fas fa-arrow-right ms-1"></i></span>
                    </a>
                 </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Add any specific JS for this page if needed -->
<?= $this->endSection() ?>